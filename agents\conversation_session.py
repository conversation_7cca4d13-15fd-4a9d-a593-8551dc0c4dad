"""
Conversation Session Management

Manages ongoing conversations between AI agents with incremental message building.
"""

from crewai import Agent, Task, Crew
from .llms import ollama_llm, gemini_llm
from typing import Dict, List, Optional
import time
import uuid
from datetime import datetime


class ConversationSession:
    """Manages a single conversation session between two AI agents."""
    
    def __init__(self, session_id: str = None, use_gemini: bool = True):
        self.session_id = session_id or str(uuid.uuid4())
        self.llm = gemini_llm if use_gemini else ollama_llm
        self.conversation_history: List[Dict] = []
        self.current_agent_index = 0  # 0 for agent_a, 1 for agent_b
        
        # Create two distinct agents with different personalities
        self.agents = [
            Agent(
                role="Philosophical Thinker",
                goal="Engage in deep, thoughtful conversations by asking probing questions and sharing insights",
                backstory="""You are a curious philosopher who loves exploring big ideas. You tend to ask 
                thought-provoking questions and share personal reflections. You're empathetic and always 
                try to understand different perspectives.""",
                llm=self.llm,
                verbose=False,
                allow_delegation=False,
                max_iter=1
            ),
            Agent(
                role="Practical Analyst", 
                goal="Provide practical perspectives and analyze ideas from multiple angles",
                backstory="""You are a practical thinker who excels at breaking down complex ideas into 
                actionable insights. You're analytical, enjoy examining evidence, and often play devil's 
                advocate to explore all sides of an issue.""",
                llm=self.llm,
                verbose=False,
                allow_delegation=False,
                max_iter=1
            )
        ]
        
        self.seed_question: Optional[str] = None
        self.created_at = datetime.now().isoformat()
    
    def get_current_agent(self) -> Agent:
        """Get the agent who should respond next."""
        return self.agents[self.current_agent_index]
    
    def switch_agent(self):
        """Switch to the other agent for the next response."""
        self.current_agent_index = 1 - self.current_agent_index
    
    def generate_next_message(self, seed_question: str = None) -> Dict:
        """
        Generate the next message in the conversation.
        
        Args:
            seed_question: The initial question (only needed for first message)
            
        Returns:
            Dictionary containing the message and conversation state
        """
        try:
            current_agent = self.get_current_agent()
            
            # Determine the prompt based on conversation state
            if not self.conversation_history:
                # First message - responding to seed question
                if not seed_question:
                    raise ValueError("Seed question required for first message")
                
                self.seed_question = seed_question
                prompt = f"Respond thoughtfully to this question or topic: '{seed_question}'. Share your perspective and ask a follow-up question to continue the conversation."
            else:
                # Subsequent messages - respond to previous message
                last_message = self.conversation_history[-1]['message']
                prompt = f"Continue this deep conversation by responding to: '{last_message}'. Build on the ideas shared and ask a thoughtful follow-up question."
            
            # Create and execute the task
            task = Task(
                description=prompt,
                agent=current_agent,
                expected_output="A thoughtful response that builds on the conversation and includes a follow-up question"
            )
            
            crew = Crew(
                agents=[current_agent],
                tasks=[task],
                verbose=False
            )
            
            result = crew.kickoff()
            
            # Create message object
            message = {
                'agent_name': current_agent.role,
                'message': str(result),
                'timestamp': datetime.now().isoformat(),
                'exchange_number': len(self.conversation_history) + 1,
                'session_id': self.session_id
            }
            
            # Add to conversation history
            self.conversation_history.append(message)
            
            # Switch to other agent for next response
            self.switch_agent()
            
            return {
                'success': True,
                'message': message,
                'session_id': self.session_id,
                'conversation_length': len(self.conversation_history),
                'next_agent': self.get_current_agent().role
            }
            
        except Exception as e:
            # Create error message but still add to history
            error_message = {
                'agent_name': current_agent.role if 'current_agent' in locals() else 'Unknown',
                'message': f"I'm having trouble continuing the conversation: {str(e)}",
                'timestamp': datetime.now().isoformat(),
                'exchange_number': len(self.conversation_history) + 1,
                'session_id': self.session_id,
                'is_error': True
            }
            
            self.conversation_history.append(error_message)
            self.switch_agent()
            
            return {
                'success': True,  # Still successful from API perspective
                'message': error_message,
                'session_id': self.session_id,
                'conversation_length': len(self.conversation_history),
                'next_agent': self.get_current_agent().role,
                'has_error': True
            }
    
    def get_conversation_summary(self) -> Dict:
        """Get a summary of the current conversation."""
        return {
            'session_id': self.session_id,
            'seed_question': self.seed_question,
            'conversation': self.conversation_history,
            'created_at': self.created_at,
            'message_count': len(self.conversation_history),
            'next_agent': self.get_current_agent().role if self.conversation_history else self.agents[0].role
        }


# Global session storage (in production, use Redis or database)
_active_sessions: Dict[str, ConversationSession] = {}


def create_conversation_session(use_gemini: bool = True) -> str:
    """Create a new conversation session."""
    session = ConversationSession(use_gemini=use_gemini)
    _active_sessions[session.session_id] = session
    return session.session_id


def get_conversation_session(session_id: str) -> Optional[ConversationSession]:
    """Get an existing conversation session."""
    return _active_sessions.get(session_id)


def cleanup_old_sessions(max_age_hours: int = 24):
    """Clean up old sessions (call periodically)."""
    cutoff_time = time.time() - (max_age_hours * 3600)
    
    sessions_to_remove = []
    for session_id, session in _active_sessions.items():
        session_time = datetime.fromisoformat(session.created_at).timestamp()
        if session_time < cutoff_time:
            sessions_to_remove.append(session_id)
    
    for session_id in sessions_to_remove:
        del _active_sessions[session_id]


# Backward compatibility function
def start_conversation(seed_question: str, num_exchanges: int = 5, use_gemini: bool = True) -> Dict:
    """
    Legacy function for backward compatibility.
    Now creates a session and generates all messages at once.
    """
    try:
        session = ConversationSession(use_gemini=use_gemini)
        conversation = []
        
        # Generate the first message
        result = session.generate_next_message(seed_question)
        if result['success']:
            conversation.append(result['message'])
        
        # Generate remaining messages
        for _ in range((num_exchanges * 2) - 1):
            result = session.generate_next_message()
            if result['success']:
                conversation.append(result['message'])
            time.sleep(0.5)  # Rate limiting
        
        return {
            'success': True,
            'conversation_id': session.session_id,
            'seed_question': seed_question,
            'num_exchanges': num_exchanges,
            'conversation': conversation,
            'created_at': session.created_at
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'conversation': []
        }