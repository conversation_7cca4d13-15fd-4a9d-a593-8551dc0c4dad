# Productivity Tools Dashboard - Vue.js Frontend

This project has been migrated from Streamlit to a modern Vue.js frontend with FastAPI backend architecture.

## 🚀 Architecture Overview

- **Frontend**: Vue.js 3 with Vite, Vue Router, and Pinia for state management
- **Backend**: FastAPI with Python for API endpoints
- **UI**: Modern, responsive design with custom CSS
- **Data**: JSON files for tasks, markdown files for notes

## 📁 Project Structure

```
agentic-ai-tools/
├── frontend/                 # Vue.js application
│   ├── src/
│   │   ├── components/      # Reusable Vue components
│   │   ├── views/          # Page components
│   │   ├── router/         # Vue Router configuration
│   │   ├── App.vue         # Root component
│   │   └── main.js         # Entry point
│   ├── package.json        # Frontend dependencies
│   └── vite.config.js      # Vite configuration
├── backend.py              # FastAPI backend server
├── requirements_backend.txt # Backend dependencies
├── agents/                 # AI agents (unchanged)
├── notes/                  # Markdown notes storage
├── tasks.json             # Tasks data storage
└── README_VUEJS.md        # This file
```

## 🛠️ Installation & Setup

### Prerequisites

- Node.js 16+ and npm
- Python 3.8+
- Git

### 1. Install Frontend Dependencies

```bash
cd frontend
npm install
```

### 2. Install Backend Dependencies

```bash
# In the root directory
pip install -r requirements_backend.txt
```

### 3. Build Frontend for Production

```bash
cd frontend
npm run build
```

## 🎯 Running the Application

### Development Mode

#### Option 1: Full Stack Development (Recommended)

**Terminal 1** - Start the backend:
```bash
python backend.py
```

**Terminal 2** - Start the frontend dev server:
```bash
cd frontend
npm run dev
```

- Backend: http://localhost:8000
- Frontend: http://localhost:3000
- API docs: http://localhost:8000/docs

#### Option 2: Production Mode

```bash
# Build frontend first
cd frontend
npm run build
cd ..

# Start backend (serves both API and built frontend)
python backend.py
```

- Full application: http://localhost:8000

## 🎨 Features

### 🏠 Dashboard
- Modern card-based interface
- Tool navigation with descriptions
- Responsive design

### 📝 Text Readability Editor
- AI-powered text processing using local LLM
- Real-time Flesch reading ease scoring
- Side-by-side input/output layout
- Processing time metrics

### ✅ Task Tracker
- CRUD operations for tasks
- Priority levels (High/Medium/Low)
- Status filtering (All/Pending/Completed)
- Search functionality
- Completion statistics

### 📄 Markdown Notepad
- Markdown editing with live preview
- Note management (create, edit, delete)
- Search through notes
- Auto-save functionality

### 🎤 Audio Recorder
- Browser-based audio recording
- Playback controls
- Download recordings
- Recording history
- Audio file analysis

### 🔐 Password Generator
- Customizable password generation
- Strength indicator
- Copy to clipboard
- Security tips
- Multiple character set options

## 🔌 API Endpoints

### Text Readability
- `POST /api/text-readability` - Process text for readability

### Tasks
- `GET /api/tasks` - Get all tasks
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/{timestamp}` - Update task
- `DELETE /api/tasks/{timestamp}` - Delete task

### Notes
- `GET /api/notes` - Get all notes
- `POST /api/notes` - Save note
- `DELETE /api/notes/{filename}` - Delete note

## 🏗️ Development

### Frontend Development

```bash
cd frontend

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Backend Development

The FastAPI backend includes:

- Automatic API documentation at `/docs`
- CORS enabled for frontend development
- Static file serving for production
- Error handling and validation
- File-based data persistence

### Adding New Features

1. **Frontend**: Create Vue components in `frontend/src/components/` or `frontend/src/views/`
2. **Backend**: Add new endpoints in `backend.py`
3. **Routing**: Update `frontend/src/router/index.js` for new pages

## 🎨 Styling

The application uses custom CSS with:

- CSS Grid and Flexbox for layouts
- CSS Variables for consistent theming
- Responsive design with mobile-first approach
- Smooth transitions and hover effects
- Modern card-based UI components

## 🔧 Configuration

### Frontend Configuration

- **Vite Config**: `frontend/vite.config.js`
- **API Proxy**: Configured to proxy `/api` requests to `localhost:8000`
- **Build Output**: `frontend/dist/`

### Backend Configuration

- **CORS**: Configured for `http://localhost:3000` in development
- **Static Files**: Serves built frontend from `frontend/dist/`
- **Data Storage**: Uses local JSON and markdown files

## 📱 Mobile Support

The application is fully responsive and works on:

- Desktop browsers
- Tablets
- Mobile phones
- Progressive Web App features

## 🚀 Deployment

### Production Deployment

1. Build the frontend:
   ```bash
   cd frontend
   npm run build
   ```

2. Install backend dependencies:
   ```bash
   pip install -r requirements_backend.txt
   ```

3. Run the backend:
   ```bash
   python backend.py
   ```

The backend will serve both the API and the built frontend application.

### Docker Deployment (Optional)

Create a `Dockerfile`:

```dockerfile
FROM node:16 AS frontend-build
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ ./
RUN npm run build

FROM python:3.9
WORKDIR /app
COPY requirements_backend.txt .
RUN pip install -r requirements_backend.txt
COPY . .
COPY --from=frontend-build /app/frontend/dist ./frontend/dist
EXPOSE 8000
CMD ["python", "backend.py"]
```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `vite.config.js` or `backend.py`
2. **CORS errors**: Ensure backend CORS settings match frontend URL
3. **Build errors**: Check Node.js version and npm install
4. **API errors**: Verify backend dependencies are installed

### Debug Mode

- Frontend: Check browser console for errors
- Backend: FastAPI provides detailed error messages at `/docs`

## 🆚 Migration from Streamlit

### What Changed

- **UI Framework**: Streamlit → Vue.js 3
- **Backend**: Streamlit server → FastAPI
- **State Management**: Streamlit session state → Pinia stores
- **Routing**: Streamlit multipage → Vue Router
- **Styling**: Streamlit components → Custom CSS

### What Stayed the Same

- All core functionality preserved
- AI agents and business logic intact
- Data storage format unchanged
- File structure for notes and tasks

### Benefits of Migration

- ⚡ **Performance**: Faster loading and interactions
- 📱 **Mobile**: Better mobile responsiveness
- 🎨 **UI/UX**: Modern, professional interface
- 🔧 **Customization**: Full control over styling and behavior
- 🚀 **Scalability**: Easier to extend and maintain
- 🌐 **Deployment**: More flexible deployment options

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test both frontend and backend
5. Submit a pull request

## 📄 License

Same license as the original project.

---

**Enjoy your new modern productivity tools dashboard!** 🎉 