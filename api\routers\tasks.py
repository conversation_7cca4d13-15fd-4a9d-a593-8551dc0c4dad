"""
Tasks Router

Handles task management CRUD operations.
"""

from datetime import datetime
from typing import List
from fastapi import APIRouter, HTTPException
from database import (
    add_task, get_tasks, update_task as db_update_task, 
    delete_task as db_delete_task
)
from api.models import Task, TaskCreate, TaskUpdate, SuccessResponse
from api.utils import handle_database_error

router = APIRouter(prefix="/api/tasks", tags=["tasks"])


@router.get("", response_model=List[Task])
@router.get("/", response_model=List[Task])
async def get_all_tasks() -> List[Task]:
    """
    Get all tasks.
    
    Returns:
        List of all tasks
        
    Raises:
        HTTPException: If retrieval fails
    """
    try:
        tasks_data = get_tasks()
        return [Task(**task) for task in tasks_data]
    except Exception as e:
        raise handle_database_error("retrieving tasks", e)


@router.post("", response_model=Task)
@router.post("/", response_model=Task)
async def create_task(task_in: TaskCreate) -> Task:
    """
    Create a new task.
    
    Args:
        task_in: Task creation data
        
    Returns:
        Created task with database ID
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        task_id = add_task(
            text=task_in.text,
            notes=task_in.notes,
            timestamp=datetime.now(),
            status=task_in.status,
            completion_timestamp=task_in.completion_timestamp,
            priority=task_in.priority
        )
        
        # Retrieve the newly created task
        created_task = next((t for t in get_tasks() if str(t['_id']) == task_id), None)
        if not created_task:
            raise HTTPException(status_code=500, detail="Task creation failed")
            
        return Task(**created_task)
        
    except Exception as e:
        raise handle_database_error("creating task", e)


@router.put("/{task_id}", response_model=Task)
async def update_task(task_id: str, task_in: TaskUpdate) -> Task:
    """
    Update an existing task.
    
    Args:
        task_id: ID of the task to update
        task_in: Task update data
        
    Returns:
        Updated task
        
    Raises:
        HTTPException: If update fails or task not found
    """
    try:
        db_update_task(
            task_id=task_id,
            text=task_in.text,
            notes=task_in.notes,
            timestamp=datetime.now(),  # Update timestamp on modification
            status=task_in.status,
            completion_timestamp=task_in.completion_timestamp,
            priority=task_in.priority
        )
        
        # Retrieve the updated task
        updated_task = next((t for t in get_tasks() if str(t['_id']) == task_id), None)
        if not updated_task:
            raise HTTPException(status_code=404, detail="Task not found")
            
        return Task(**updated_task)
        
    except Exception as e:
        raise handle_database_error("updating task", e)


@router.delete("/{task_id}", response_model=SuccessResponse)
async def delete_task(task_id: str) -> SuccessResponse:
    """
    Delete a task.
    
    Args:
        task_id: ID of the task to delete
        
    Returns:
        Success confirmation message
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        db_delete_task(task_id)
        return SuccessResponse(message="Task deleted successfully")
        
    except Exception as e:
        raise handle_database_error("deleting task", e)