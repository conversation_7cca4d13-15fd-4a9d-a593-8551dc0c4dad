import os
from crewai import LLM
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure the Ollama model
ollama_llm = LLM(model="ollama/phi4:14b", base_url="http://localhost:11434")

# Configure the Gemini model
gemini_api_key = os.getenv("GEMINI_KEY")
gemini_model_name = os.getenv("GEMINI_MODEL", "gemini-2.5-flash")

gemini_llm = LLM(
    model=f"gemini/{gemini_model_name}",
    api_key=gemini_api_key
)