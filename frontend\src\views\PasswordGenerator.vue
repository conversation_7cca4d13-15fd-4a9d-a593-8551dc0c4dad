<template>
  <div class="container">
    <hgroup>
      <h1>Secure Password Generator</h1>
      <p>Generate strong, secure passwords with customizable options</p>
    </hgroup>

    <div class="grid">
      <article class="col">
        <h3>Password Settings</h3>
        <label for="length">Password Length</label>
        <input
          v-model.number="settings.length"
          type="range"
          id="length"
          name="length"
          min="4"
          max="128"
        />
        <p>Current Length: {{ settings.length }}</p>

        <fieldset>
          <legend>Include Characters:</legend>
          <label>
            <input v-model="settings.includeUppercase" type="checkbox" />
            Uppercase Letters (A-Z)
          </label>
          <label>
            <input v-model="settings.includeLowercase" type="checkbox" />
            Lowercase Letters (a-z)
          </label>
          <label>
            <input v-model="settings.includeNumbers" type="checkbox" />
            Numbers (0-9)
          </label>
          <label>
            <input v-model="settings.includeSymbols" type="checkbox" />
            Symbols (!@#$%^&*)
          </label>
        </fieldset>

        <button @click="generatePassword">Generate Password</button>
      </article>

      <article class="col">
        <h3>Generated Password</h3>
        <div class="grid">
          <div class="col">
            <input
              v-model="generatedPassword"
              type="text"
              readonly
              placeholder="Click 'Generate Password' to create a new password"
            />
          </div>
          <div class="col">
            <button
              v-if="generatedPassword"
              @click="copyPassword"
              :class="{ 'secondary outline': !isCopied }"
            >
              {{ isCopied ? "Copied!" : "Copy" }}
            </button>
          </div>
        </div>

        <div v-if="passwordStrength">
          <progress :value="passwordStrength.percentage" max="100"></progress>
          <p>
            Strength: <strong>{{ passwordStrength.text }}</strong>
          </p>
        </div>

        <details v-if="generatedPassword">
          <summary>Security Tips:</summary>
          <ul>
            <li>Store your password in a secure password manager</li>
            <li>Don't reuse passwords across multiple accounts</li>
            <li>Consider enabling two-factor authentication</li>
            <li>Don't share your password with anyone</li>
          </ul>
        </details>
      </article>
    </div>
  </div>
</template>

<script>
export default {
  name: "PasswordGenerator",
  data() {
    return {
      settings: {
        length: 16,
        includeUppercase: true,
        includeLowercase: true,
        includeNumbers: true,
        includeSymbols: true,
      },
      generatedPassword: "",
      isCopied: false,
      passwordStrength: null,
    };
  },
  methods: {
    generatePassword() {
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const numbers = "**********";
      const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";

      let characters = "";
      let password = "";

      if (this.settings.includeUppercase) characters += uppercase;
      if (this.settings.includeLowercase) characters += lowercase;
      if (this.settings.includeNumbers) characters += numbers;
      if (this.settings.includeSymbols) characters += symbols;

      if (!characters) {
        alert("Please select at least one character type");
        return;
      }

      // Ensure at least one character from each selected type
      if (this.settings.includeUppercase) {
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
      }
      if (this.settings.includeLowercase) {
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
      }
      if (this.settings.includeNumbers) {
        password += numbers[Math.floor(Math.random() * numbers.length)];
      }
      if (this.settings.includeSymbols) {
        password += symbols[Math.floor(Math.random() * symbols.length)];
      }

      // Fill the rest of the password
      for (let i = password.length; i < this.settings.length; i++) {
        password += characters[Math.floor(Math.random() * characters.length)];
      }

      // Shuffle the password
      this.generatedPassword = password
        .split("")
        .sort(() => 0.5 - Math.random())
        .join("");
      this.calculateStrength();
      this.isCopied = false;
    },

    async copyPassword() {
      try {
        await navigator.clipboard.writeText(this.generatedPassword);
        this.isCopied = true;
        setTimeout(() => {
          this.isCopied = false;
        }, 2000);
      } catch (err) {
        console.error("Failed to copy password:", err);
      }
    },

    calculateStrength() {
      const password = this.generatedPassword;
      let score = 0;

      // Length scoring
      if (password.length >= 8) score += 1;
      if (password.length >= 12) score += 1;
      if (password.length >= 16) score += 1;

      // Character variety scoring
      if (/[a-z]/.test(password)) score += 1;
      if (/[A-Z]/.test(password)) score += 1;
      if (/[0-9]/.test(password)) score += 1;
      if (/[^A-Za-z0-9]/.test(password)) score += 1;

      if (score <= 2) {
        this.passwordStrength = { level: "weak", text: "Weak", percentage: 25 };
      } else if (score <= 4) {
        this.passwordStrength = {
          level: "medium",
          text: "Medium",
          percentage: 50,
        };
      } else if (score <= 6) {
        this.passwordStrength = {
          level: "strong",
          text: "Strong",
          percentage: 75,
        };
      } else {
        this.passwordStrength = {
          level: "very-strong",
          text: "Very Strong",
          percentage: 100,
        };
      }
    },
  },

  mounted() {
    this.generatePassword();
  },
};
</script>

<style scoped>
/* No scoped styles needed as Pico.css handles most of it */
</style>
