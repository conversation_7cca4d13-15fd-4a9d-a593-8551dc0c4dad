Project Name,File Path,Language,Total Lines,Code Lines,Comment Lines,Blank Lines,File Size (bytes),20250811
,run.bat,Batch,20,16,0,4,378,20250811
,frontend\src\style.css,CSS,158,82,61,15,3127,20250811
,frontend\index.html,HTML,14,14,0,0,501,20250811
,frontend\package-lock.json,JSON,1495,336,1159,0,51071,20250811
,frontend\package.json,JSON,21,5,16,0,434,20250811
,frontend\src\main.js,JavaScript,12,9,0,3,245,20250811
,frontend\src\router\index.js,JavaScript,47,44,0,3,1126,20250811
,frontend\vite.config.js,JavaScript,15,14,0,1,284,20250811
,README.md,Mark<PERSON>,36,19,4,13,956,20250811
,api\README.md,<PERSON><PERSON>,86,49,17,20,2795,20250811
,frontend\README.md,Mark<PERSON>,317,181,51,85,7717,20250811
,agents\__init__.py,Python,1,1,0,0,56,20250811
,agents\conversation_session.py,Python,226,28,157,41,9121,20250811
,agents\filename_generation_agent.py,Python,49,32,10,7,1717,20250811
,agents\flesch_reading_ease_score_agent.py,Python,69,41,19,9,2712,20250811
,agents\llm_accuracy_agent.py,Python,151,47,81,23,5611,20250811
,agents\llm_conversation_agent.py,Python,118,89,13,16,5166,20250811
,agents\llms.py,Python,18,11,3,4,457,20250811
,api\__init__.py,Python,0,0,0,0,0,20250811
,api\models.py,Python,199,63,89,47,5785,20250811
,api\routers\__init__.py,Python,0,0,0,0,0,20250811
,api\routers\conversations.py,Python,261,161,57,43,8882,20250811
,api\routers\notes.py,Python,171,72,58,41,4503,20250811
,api\routers\tasks.py,Python,131,61,41,29,3676,20250811
,api\routers\text_readability.py,Python,55,30,13,12,1894,20250811
,api\utils.py,Python,117,56,35,26,3668,20250811
,backend.py,Python,1,1,0,0,7,20250811
,backend_original.py,Python,380,227,107,46,14177,20250811
,database.py,Python,159,107,30,22,5225,20250811
,main.py,Python,85,37,29,19,2533,20250811
,scripts\loc_counter.py,Python,396,201,132,63,15073,20250811
,test_auto_flow.py,Python,83,61,5,17,3490,20250811
,test_chat_flow.py,Python,79,60,4,15,3469,20250811
,run.sh,Shell,18,13,1,4,371,20250811
,frontend\src\App.vue,Vue,36,31,2,3,696,20250811
,frontend\src\components\ThemeToggle.vue,Vue,71,51,17,3,2765,20250811
,frontend\src\components\ToolCard.vue,Vue,37,33,2,2,618,20250811
,frontend\src\views\Dashboard.vue,Vue,83,77,2,4,2307,20250811
,frontend\src\views\DeepConversations.vue,Vue,666,445,141,80,17364,20250811
,frontend\src\views\Notepad.vue,Vue,350,310,9,31,9317,20250811
,frontend\src\views\PasswordGenerator.vue,Vue,212,185,7,20,6329,20250811
,frontend\src\views\TaskTracker.vue,Vue,345,318,6,21,9722,20250811
,frontend\src\views\TextReadabilityEditor.vue,Vue,100,92,1,7,2634,20250811
,,,,,,,,
SUMMARY BY LANGUAGE,,,,,,,,
Language,Files,Total Lines,Code Lines,Comment Lines,Blank Lines,,,
Python,22,2749,1386,883,480,,,
Vue,9,1900,1542,187,171,,,
JSON,2,1516,341,1175,0,,,
Markdown,3,439,249,72,118,,,
CSS,1,158,82,61,15,,,
JavaScript,3,74,67,0,7,,,
Batch,1,20,16,0,4,,,
Shell,1,18,13,1,4,,,
HTML,1,14,14,0,0,,,
,,,,,,,,
TOTAL,43,6888,3710,2379,799,,,
