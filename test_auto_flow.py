#!/usr/bin/env python3

from main import app
from fastapi.testclient import TestClient
import time

client = TestClient(app)

print("Testing auto-flowing conversation simulation...")

# Test starting a conversation
print("\n1. Starting conversation...")
start_response = client.post('/api/conversations/start-chat', json={
    'seed_question': 'What is creativity?',
    'use_gemini': False
})

print(f"Status: {start_response.status_code}")
if start_response.status_code == 200:
    start_data = start_response.json()
    if start_data.get('success'):
        session_id = start_data.get('session_id')
        print(f"✅ First message received from: {start_data['message']['agent_name']}")
        
        # Simulate auto-flowing by continuing the conversation multiple times
        message_count = 1
        target_messages = 5
        
        print(f"\n2. Auto-flowing to {target_messages} messages...")
        
        while message_count < target_messages:
            print(f"\n   Getting message {message_count + 1}...")
            
            continue_response = client.post('/api/conversations/continue', json={
                'session_id': session_id
            })
            
            if continue_response.status_code == 200:
                continue_data = continue_response.json()
                if continue_data.get('success'):
                    message = continue_data['message']
                    conv_length = continue_data['conversation_length']
                    next_agent = continue_data['next_agent']
                    
                    print(f"   ✅ Message {conv_length} from: {message['agent_name']}")
                    print(f"   Next agent: {next_agent}")
                    print(f"   Message length: {len(message['message'])} characters")
                    
                    message_count = conv_length
                else:
                    print(f"   ❌ Continue failed: {continue_data.get('error')}")
                    break
            else:
                print(f"   ❌ HTTP error: {continue_response.status_code}")
                break
            
            # Small delay to simulate realistic auto-flow timing
            time.sleep(0.5)
        
        # Get final conversation summary
        print(f"\n3. Getting conversation summary...")
        summary_response = client.get(f'/api/conversations/summary/{session_id}')
        
        if summary_response.status_code == 200:
            summary_data = summary_response.json()
            if summary_data.get('success'):
                print(f"✅ Final conversation summary:")
                print(f"   Seed question: {summary_data['seed_question']}")
                print(f"   Total messages: {summary_data['message_count']}")
                print(f"   Session ID: {summary_data['session_id']}")
                
                print(f"\n🎉 Auto-flowing conversation test completed successfully!")
                print(f"   Generated {summary_data['message_count']} messages automatically")
            else:
                print(f"❌ Summary failed: {summary_data.get('error')}")
        else:
            print(f"❌ Summary HTTP error: {summary_response.status_code}")
            
    else:
        print(f"❌ Start failed: {start_data.get('error')}")
else:
    print(f"❌ Start HTTP error: {start_response.status_code}")
    print(start_response.text)