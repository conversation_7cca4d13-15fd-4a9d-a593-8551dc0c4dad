from pymongo.mongo_client import MongoClient
from pymongo.server_api import Server<PERSON><PERSON>
from datetime import datetime
from bson.objectid import ObjectId

# MongoDB connection URI
uri = "mongodb+srv://muthukrishnan749:<EMAIL>/?retryWrites=true&w=majority&appName=agentic-ai-tools"

# Create a new client and connect to the server
client = MongoClient(uri, server_api=ServerApi('1'))

# Send a ping to confirm a successful connection
try:
    client.admin.command('ping')
    print("Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)

def get_notes_collection():
    db = client.get_database("productivity_tools")
    return db.notes

def get_gitlab_loc_collection():
    db = client.get_database("gitlab_loc")
    return db.loc

def add_note(title, content, tags):
    notes_collection = get_notes_collection()
    note = {
        "title": title,
        "content": content,
        "tags": tags.split(',') if tags else [],
        "created_at": datetime.now(),
        "last_modified": datetime.now()
    }
    result = notes_collection.insert_one(note)
    return str(result.inserted_id)

def add_gitlab_loc_bulk(data):
    collection = get_gitlab_loc_collection()
    for doc in data:
        collection.update_one(
            {"timestamp": doc["timestamp"]},
            {"$set": doc},
            upsert=True
        )

def get_notes(search_query=None, tags=None):
    notes_collection = get_notes_collection()
    query = {}
    if search_query:
        query["$or"] = [
            {"title": {"$regex": search_query, "$options": "i"}},
            {"content": {"$regex": search_query, "$options": "i"}}
        ]
    if tags:
        query["tags"] = {"$in": [tag.strip() for tag in tags.split(',')]}
    
    notes = []
    for note in notes_collection.find(query).sort("last_modified", -1):
        note["_id"] = str(note["_id"]) # Convert ObjectId to string
        notes.append(note)
    return notes

def get_note(note_id):
    notes_collection = get_notes_collection()
    note = notes_collection.find_one({"_id": ObjectId(note_id)})
    if note:
        note["_id"] = str(note["_id"])
    return note

def update_note(note_id, title, content, tags):
    notes_collection = get_notes_collection()
    update_data = {
        "title": title,
        "content": content,
        "tags": tags.split(',') if tags else [],
        "last_modified": datetime.now()
    }
    notes_collection.update_one({"_id": ObjectId(note_id)}, {"$set": update_data})

def delete_note(note_id):
    notes_collection = get_notes_collection()
    notes_collection.delete_one({"_id": ObjectId(note_id)})

def get_all_tags():
    notes_collection = get_notes_collection()
    tags = notes_collection.distinct("tags")
    return sorted(list(tags))

def get_tasks_collection():
    db = client.get_database("productivity_tools")
    return db.tasks

def add_task(text, notes, timestamp, status, completion_timestamp, priority):
    tasks_collection = get_tasks_collection()
    task = {
        "text": text,
        "notes": notes,
        "timestamp": timestamp,
        "status": status,
        "completion_timestamp": completion_timestamp,
        "priority": priority
    }
    result = tasks_collection.insert_one(task)
    return str(result.inserted_id)

def get_tasks():
    tasks_collection = get_tasks_collection()
    tasks = []
    for task in tasks_collection.find().sort("timestamp", -1):
        task["_id"] = str(task["_id"])
        tasks.append(task)
    return tasks

def update_task(task_id, text, notes, timestamp, status, completion_timestamp, priority):
    tasks_collection = get_tasks_collection()
    update_data = {
        "text": text,
        "notes": notes,
        "timestamp": timestamp,
        "status": status,
        "completion_timestamp": completion_timestamp,
        "priority": priority
    }
    tasks_collection.update_one({"_id": ObjectId(task_id)}, {"$set": update_data})

def delete_task(task_id):
    tasks_collection = get_tasks_collection()
    tasks_collection.delete_one({"_id": ObjectId(task_id)})

def get_conversations_collection():
    db = client.get_database("productivity_tools")
    return db.conversations

def save_conversation(session_id, seed_question, conversation_messages, created_at):
    conversations_collection = get_conversations_collection()
    conversation = {
        "session_id": session_id,
        "seed_question": seed_question,
        "conversation": conversation_messages,
        "created_at": created_at,
        "message_count": len(conversation_messages),
        "saved_at": datetime.now()
    }
    result = conversations_collection.insert_one(conversation)
    return str(result.inserted_id)

def get_conversations():
    conversations_collection = get_conversations_collection()
    conversations = []
    for conv in conversations_collection.find().sort("saved_at", -1):
        conv["_id"] = str(conv["_id"])
        conversations.append(conv)
    return conversations

def delete_conversation(conversation_id):
    conversations_collection = get_conversations_collection()
    conversations_collection.delete_one({"_id": ObjectId(conversation_id)})