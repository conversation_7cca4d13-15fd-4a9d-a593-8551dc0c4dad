#!/usr/bin/env python3

from main import app
from fastapi.testclient import TestClient

client = TestClient(app)

print("Testing new chat-like conversation flow...")

# Test starting a chat conversation
print("\n1. Starting conversation...")
start_response = client.post('/api/conversations/start-chat', json={
    'seed_question': 'What makes a good conversation?',
    'use_gemini': False
})

print(f"Status: {start_response.status_code}")
if start_response.status_code == 200:
    start_data = start_response.json()
    print(f"Success: {start_data.get('success')}")
    
    if start_data.get('success'):
        session_id = start_data.get('session_id')
        message = start_data.get('message')
        next_agent = start_data.get('next_agent')
        
        print(f"Session ID: {session_id}")
        print(f"First message from: {message.get('agent_name')}")
        print(f"Message length: {len(message.get('message', ''))}")
        print(f"Next agent: {next_agent}")
        
        # Test continuing the conversation
        print("\n2. Continuing conversation...")
        continue_response = client.post('/api/conversations/continue', json={
            'session_id': session_id
        })
        
        print(f"Status: {continue_response.status_code}")
        if continue_response.status_code == 200:
            continue_data = continue_response.json()
            print(f"Success: {continue_data.get('success')}")
            
            if continue_data.get('success'):
                message2 = continue_data.get('message')
                next_agent2 = continue_data.get('next_agent')
                conv_length = continue_data.get('conversation_length')
                
                print(f"Second message from: {message2.get('agent_name')}")
                print(f"Message length: {len(message2.get('message', ''))}")
                print(f"Next agent: {next_agent2}")
                print(f"Conversation length: {conv_length}")
                
                # Test getting conversation summary
                print("\n3. Getting conversation summary...")
                summary_response = client.get(f'/api/conversations/summary/{session_id}')
                
                print(f"Status: {summary_response.status_code}")
                if summary_response.status_code == 200:
                    summary_data = summary_response.json()
                    print(f"Success: {summary_data.get('success')}")
                    
                    if summary_data.get('success'):
                        print(f"Seed question: {summary_data.get('seed_question')}")
                        print(f"Total messages: {summary_data.get('message_count')}")
                        print(f"Next agent: {summary_data.get('next_agent')}")
                        
                        print("\n✅ All tests passed! Chat-like conversation flow works correctly.")
                    else:
                        print(f"Summary error: {summary_data.get('error')}")
                else:
                    print(f"Summary failed: {summary_response.text}")
            else:
                print(f"Continue error: {continue_data.get('error')}")
        else:
            print(f"Continue failed: {continue_response.text}")
    else:
        print(f"Start error: {start_data.get('error')}")
else:
    print(f"Start failed: {start_response.text}")