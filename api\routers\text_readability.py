"""
Text Readability Router

Handles text readability analysis and improvement endpoints.
"""

import time
import textstat
from fastapi import APIRouter, HTTPException
from agents.flesch_reading_ease_score_agent import change_reading_score
from api.models import TextReadabilityRequest, TextReadabilityResponse
from api.utils import handle_database_error

router = APIRouter(prefix="/api/text-readability", tags=["text-readability"])


@router.post("", response_model=TextReadabilityResponse)
@router.post("/", response_model=TextReadabilityResponse)
async def process_text_readability(request: TextReadabilityRequest) -> TextReadabilityResponse:
    """
    Process text for readability improvement.
    
    Args:
        request: Text readability request containing text and target score
        
    Returns:
        TextReadabilityResponse with processed text and metrics
        
    Raises:
        HTTPException: If text readability feature is unavailable or processing fails
    """
    if not change_reading_score:
        raise HTTPException(
            status_code=501, 
            detail="Text readability feature is not available"
        )
    
    try:
        start_time = time.time()
        processed_text = change_reading_score(request.text, request.target_score)
        end_time = time.time()
        
        current_score = textstat.flesch_reading_ease(processed_text)
        grade_level = textstat.flesch_kincaid_grade(processed_text)
        processing_time = end_time - start_time
        
        return TextReadabilityResponse(
            processed_text=processed_text,
            current_score=current_score,
            grade_level=grade_level,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise handle_database_error("processing text readability", e)