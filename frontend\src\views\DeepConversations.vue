<template>
  <div class="container">
    <hgroup>
      <h1>Deep Conversations</h1>
      <p>
        Watch two AI agents engage in thoughtful dialogue on topics that matter
      </p>
    </hgroup>

    <div class="row">
      <div class="col-4">
        <!-- Controls Section -->
        <article class="conversation-controls">
          <h3>Start a New Conversation</h3>
          <div class="grid">
            <label for="seed-question">
              Seed Question or Topic
              <input
                v-model="seedQuestion"
                type="text"
                id="seed-question"
                placeholder="What is the meaning of a fulfilling life?"
                :disabled="isLoading || hasActiveSession"
              />
            </label>
          </div>

          <div class="grid">
            <label for="conversation-limit">
              Conversation Limit (messages)
              <input
                v-model="conversationLimit"
                type="number"
                id="conversation-limit"
                min="1"
                max="20"
                :disabled="isLoading || hasActiveSession"
              />
            </label>

            <label for="llm-model">
              LLM Model
              <select
                v-model="useGemini"
                id="llm-model"
                :disabled="isLoading || hasActiveSession"
              >
                <option :value="true">Gemini (Recommended)</option>
                <option :value="false">Ollama Local</option>
              </select>
            </label>
          </div>

          <div class="conversation-buttons">
            <button
              v-if="!hasActiveSession"
              @click="startAutoConversation"
              :disabled="!seedQuestion.trim() || isLoading"
              :aria-busy="isLoading"
            >
              {{
                isLoading ? "Starting Conversation..." : "Start Conversation"
              }}
            </button>

            <div v-if="hasActiveSession" class="conversation-progress">
              <span class="progress-text">
                {{ conversationMessages.length }} /
                {{ conversationLimit }} messages
              </span>
              <progress
                :value="conversationMessages.length"
                :max="conversationLimit"
                class="conversation-progress-bar"
              ></progress>
            </div>

            <span v-if="isLoading" class="loading-indicator">
              {{ nextAgentName }} is thinking...
              <span v-if="isAutoFlowing"
                >(Progress
                {{
                  Math.round(
                    (conversationMessages.length * 100) / conversationLimit
                  )
                }}%)</span
              >
            </span>
          </div>
        </article>
        <!-- Error Display -->
        <article v-if="error" class="error">
          <h4>Error</h4>
          <p>{{ error }}</p>
        </article>

        <!-- Conversation History -->
        <article
          v-if="conversationHistory.length > 0"
          class="conversation-history"
        >
          <h3>Conversation History</h3>
          <div class="history-grid">
            <div
              v-for="(conv, index) in conversationHistory"
              :key="conv.session_id || index"
              class="history-item"
              @click="loadConversation(conv)"
            >
              <h5>{{ conv.seed_question }}</h5>
              <p>
                {{ conv.message_count || conv.conversation.length }} messages
              </p>
              <small>{{ formatTime(conv.created_at) }}</small>
            </div>
          </div>

          <button @click="clearHistory" class="secondary">
            Clear All History
          </button>
        </article>
      </div>
      <div class="col-8">
        <article
          v-if="conversationMessages.length > 0"
          class="conversation-display"
        >
          <header>
            <h3>
              {{ seedQuestion }}
            </h3>
          </header>

          <div class="conversation-thread" ref="conversationThread">
            <div
              v-for="(message, index) in conversationMessages"
              :key="message.session_id + '-' + message.exchange_number"
              :class="[
                'message',
                message.agent_name === 'Philosophical Thinker'
                  ? 'philosopher'
                  : 'analyst',
                message.is_error ? 'error-message' : '',
              ]"
            >
              <div class="message-header">
                <strong>{{ message.agent_name }}</strong>
              </div>
              <div class="message-content">
                {{ message.message }}
              </div>
            </div>

            <!-- Typing indicator -->
            <div v-if="isLoading" class="typing-indicator">
              <div class="message analyst">
                <div class="message-header">
                  <strong>{{ nextAgentName }}</strong>
                  <small>Thinking...</small>
                </div>
                <div class="message-content">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="conversation-actions">
            <button
              v-if="hasActiveSession && !isLoading"
              @click="continueConversation"
              class="primary"
            >
              Next Response
            </button>
            <button @click="saveCurrentConversation" class="secondary">
              Save to History
            </button>
          </div>
        </article>
      </div>
    </div>
  </div>
</template>

<script>
import { marked } from "marked";

export default {
  name: "DeepConversations",
  data() {
    return {
      seedQuestion: "",
      conversationLimit: 8,
      useGemini: true,
      isLoading: false,
      error: null,
      sessionId: null,
      conversationMessages: [],
      nextAgentName: "",
      conversationHistory: [],
      isAutoFlowing: false,
      autoFlowTimeout: null,
    };
  },
  computed: {
    hasActiveSession() {
      return this.sessionId !== null;
    },
    canContinueConversation() {
      return (
        this.hasActiveSession &&
        this.conversationMessages.length < this.conversationLimit
      );
    },
    isConversationComplete() {
      return (
        this.hasActiveSession &&
        this.conversationMessages.length >= this.conversationLimit
      );
    },
  },
  mounted() {
    this.loadHistory();
  },
  beforeUnmount() {
    // Clean up any pending timeouts
    this.pauseAutoFlow();
  },
  methods: {
    async startAutoConversation() {
      if (!this.seedQuestion.trim()) return;

      this.isLoading = true;
      this.error = null;
      this.nextAgentName = "Philosophical Thinker";
      this.isAutoFlowing = true;

      try {
        const response = await fetch("/api/conversations/start-chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            seed_question: this.seedQuestion,
            use_gemini: this.useGemini,
          }),
        });

        const data = await response.json();

        if (data.success) {
          this.sessionId = data.session_id;
          this.conversationMessages = [marked(data.message)];
          this.nextAgentName = data.next_agent;
          this.error = null;
          this.scrollToBottom();

          // Start auto-flowing to the next message
          this.scheduleNextAutoResponse();
        } else {
          this.error = data.error || "Failed to start conversation";
          this.isAutoFlowing = false;
        }
      } catch (err) {
        this.error = "Network error: " + err.message;
        this.isAutoFlowing = false;
      } finally {
        this.isLoading = false;
      }
    },

    scheduleNextAutoResponse() {
      if (!this.isAutoFlowing || !this.canContinueConversation) {
        this.isAutoFlowing = false;
        return;
      }

      // Wait 2 seconds before getting the next response for better UX
      this.autoFlowTimeout = setTimeout(() => {
        this.continueConversationAuto();
      }, 2000);
    },

    async continueConversationAuto() {
      if (!this.canContinueConversation || !this.isAutoFlowing) {
        this.isAutoFlowing = false;
        return;
      }

      await this.continueConversation();

      // Schedule the next response if we're still auto-flowing
      if (this.isAutoFlowing && this.canContinueConversation) {
        this.scheduleNextAutoResponse();
      } else {
        this.isAutoFlowing = false;
      }
    },

    pauseAutoFlow() {
      this.isAutoFlowing = false;
      if (this.autoFlowTimeout) {
        clearTimeout(this.autoFlowTimeout);
        this.autoFlowTimeout = null;
      }
    },

    resumeAutoFlow() {
      if (this.canContinueConversation) {
        this.isAutoFlowing = true;
        this.scheduleNextAutoResponse();
      }
    },

    async continueConversation() {
      if (!this.sessionId) return;

      this.isLoading = true;
      this.error = null;

      try {
        const response = await fetch("/api/conversations/continue", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            session_id: this.sessionId,
          }),
        });

        const data = await response.json();

        if (data.success) {
          this.conversationMessages.push(marked(data.message));
          this.nextAgentName = data.next_agent;
          this.error = null;
          this.scrollToBottom();
        } else {
          this.error = data.error || "Failed to continue conversation";
        }
      } catch (err) {
        this.error = "Network error: " + err.message;
      } finally {
        this.isLoading = false;
      }
    },

    endConversation() {
      this.pauseAutoFlow(); // Stop any auto-flow
      this.sessionId = null;
      this.nextAgentName = "";
      // Keep the messages for viewing but end the active session
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const thread = this.$refs.conversationThread;
        if (thread) {
          thread.scrollTop = thread.scrollHeight;
        }
      });
    },

    async saveCurrentConversation() {
      if (this.conversationMessages.length === 0) return;

      const conversationData = {
        session_id: this.sessionId || `saved_${Date.now()}`,
        seed_question: this.seedQuestion,
        conversation: this.conversationMessages,
        created_at: new Date().toISOString(),
        message_count: this.conversationMessages.length,
      };

      try {
        const response = await fetch("/api/conversations/save", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(conversationData),
        });

        const data = await response.json();

        if (data.success) {
          // Also save locally for immediate UI update
          const existingIndex = this.conversationHistory.findIndex(
            (conv) => conv.session_id === conversationData.session_id
          );

          if (existingIndex === -1) {
            this.conversationHistory.unshift(conversationData);
            // Keep only last 10 conversations in local storage
            if (this.conversationHistory.length > 10) {
              this.conversationHistory = this.conversationHistory.slice(0, 10);
            }
            this.saveHistory();
          }

          this.error = null;
        } else {
          this.error = data.error || "Failed to save conversation";
        }
      } catch (err) {
        this.error = "Network error: " + err.message;
      }
    },

    loadConversation(conversation) {
      this.conversationMessages = conversation.conversation;
      this.seedQuestion = conversation.seed_question;
      this.sessionId = null; // Historical conversation, not active
      this.nextAgentName = "";
      this.error = null;
    },

    clearConversation() {
      this.pauseAutoFlow(); // Stop any auto-flow
      this.conversationMessages = [];
      this.sessionId = null;
      this.seedQuestion = "";
      this.nextAgentName = "";
      this.error = null;
    },

    clearHistory() {
      this.conversationHistory = [];
      this.saveHistory();
    },

    saveHistory() {
      localStorage.setItem(
        "deepConversationsHistory",
        JSON.stringify(this.conversationHistory)
      );
    },

    loadHistory() {
      const saved = localStorage.getItem("deepConversationsHistory");
      if (saved) {
        try {
          this.conversationHistory = JSON.parse(saved);
        } catch (e) {
          console.error("Error loading conversation history:", e);
        }
      }
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString();
    },
  },
};
</script>

<style scoped>
.conversation-controls {
  margin-bottom: 2rem;
}

.conversation-display {
  margin-bottom: 2rem;
}

.conversation-thread {
  border: 1px solid var(--muted-border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
}

.message {
  margin-bottom: 1.5rem;
  padding: 1rem;
}

.message.philosopher {
  background-color: var(--code-background-color);
  border-left-color: var(--primary);
}

.message.analyst {
  background-color: var(--card-background-color);
  border-left-color: var(--secondary);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-content {
  margin: 0.5rem 0;
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-time {
  font-size: 0.875rem;
  color: var(--muted-color);
  text-align: right;
}

.conversation-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.conversation-history {
  margin-top: 2rem;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.history-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background-color: var(--card-background-color);
  transform: translateY(-2px);
}

.history-item h5 {
  margin: 0 0 0.5rem 0;
  color: var(--primary);
}

.history-item p {
  margin: 0.25rem 0;
  color: var(--muted-color);
}

.error {
  background-color: var(--del-color);
  color: var(--del-text-color);
  border: 1px solid var(--del-border-color);
}

.conversation-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.conversation-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--muted-color);
  text-align: center;
}

.conversation-progress-bar {
  width: 100%;
  height: 8px;
}

.conversation-progress-bar::-webkit-progress-bar {
  background-color: var(--muted-border-color);
  border-radius: 4px;
}

.conversation-progress-bar::-webkit-progress-value {
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.conversation-progress-bar::-moz-progress-bar {
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 4px;
}

.loading-indicator {
  font-style: italic;
  color: var(--muted-color);
}

.typing-indicator {
  opacity: 0.7;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  padding: 0.5rem 0;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--muted-color);
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.message.error-message {
  border-left-color: var(--del-color);
  background-color: var(--del-background-color, #fef2f2);
}

@media (max-width: 768px) {
  .conversation-actions {
    flex-direction: column;
  }

  .history-grid {
    grid-template-columns: 1fr;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

.row .col-4 {
  padding-left: 15px;
}
</style>
