# Agentic AI Tools

This repository contains a collection of AI-powered tools to assist with various tasks.

## Tools

* **Flesch Reading Ease Score Agent:** Analyzes text to determine its readability score.
* **Secure Password Generator:** Creates strong, random passwords.
* **Task Tracker:** A simple tool to track tasks.
* **Text Readability Editor:** Helps improve the readability of text.

## Usage

To run the main application, execute the following command:

```bash
python main.py
```

## Testing

To run the unit tests and generate a coverage report, follow these steps:

1. Install the testing dependencies:

```bash
pip install pytest pytest-cov
```

2. Run the tests and generate the coverage report:

```bash
pytest --cov=backend --cov-report=term-missing test_backend.py
```

This will execute all tests in `test_backend.py` and display a coverage report for `backend.py`, highlighting any missing lines.
