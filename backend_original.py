from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from agents.flesch_reading_ease_score_agent import change_reading_score
from agents import generate_filename
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from agents.flesch_reading_ease_score_agent import change_reading_score
from agents import generate_filename
from pydantic import BaseModel, Field
from typing import List, Optional, Any
import json
import os
import time
import textstat
from datetime import datetime
import sys
from database import add_note, get_notes, get_note, update_note, delete_note, get_all_tags, add_task, get_tasks, update_task as db_update_task, delete_task as db_delete_task
from bson import ObjectId
from agents.llm_conversation_agent import start_conversation

# Add the current directory to sys.path to import from agents
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = FastAPI(title="Productivity Tools API", version="1.0.0")

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
from pydantic_core import core_schema

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, _source_type: Any, _handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate_object_id),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def validate_object_id(cls, value: str) -> ObjectId:
        if not ObjectId.is_valid(value):
            raise ValueError("Invalid ObjectId format")
        return ObjectId(value)

class TextReadabilityRequest(BaseModel):
    text: str
    target_score: int

class TextReadabilityResponse(BaseModel):
    processed_text: str
    current_score: float
    grade_level: float
    processing_time: float

class Task(BaseModel):
    id: Optional[PyObjectId] = Field(alias="_id")
    text: str
    notes: str = ""
    timestamp: datetime = Field(default_factory=datetime.now)
    status: str = "pending"
    completion_timestamp: Optional[datetime] = None
    priority: str = "Medium"

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class TaskIn(BaseModel):
    text: str
    notes: str = ""
    status: str = "pending"
    completion_timestamp: Optional[datetime] = None
    priority: str = "Medium"

class Note(BaseModel):
    id: Optional[PyObjectId] = Field(alias="_id")
    title: str
    content: str
    tags: Optional[List[str]] = []
    created_at: datetime = Field(default_factory=datetime.now)
    last_modified: datetime = Field(default_factory=datetime.now)

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class NoteIn(BaseModel):
    title: str
    content: str
    tags: Optional[str] = ""

class ConversationRequest(BaseModel):
    seed_question: str
    num_exchanges: int = 5
    use_gemini: bool = True

class ConversationMessage(BaseModel):
    agent_name: str
    message: str
    timestamp: str
    exchange_number: int

class ConversationResponse(BaseModel):
    success: bool
    conversation_id: Optional[str] = None
    seed_question: Optional[str] = None
    num_exchanges: Optional[int] = None
    conversation: List[ConversationMessage] = []
    created_at: Optional[str] = None
    error: Optional[str] = None

# Data storage paths
TASKS_FILE = "tasks.json"

# One-time migration from tasks.json to MongoDB
@app.on_event("startup")
async def migrate_tasks_data():
    if os.path.exists(TASKS_FILE):
        print(f"Migrating tasks from {TASKS_FILE} to MongoDB...")
        try:
            with open(TASKS_FILE, 'r') as f:
                local_tasks = json.load(f)
            
            for task_data in local_tasks:
                # Ensure timestamp and completion_timestamp are datetime objects
                if 'timestamp' in task_data and isinstance(task_data['timestamp'], str):
                    try:
                        task_data['timestamp'] = datetime.strptime(task_data['timestamp'], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        task_data['timestamp'] = datetime.now() # Fallback if format is unexpected
                else:
                    task_data['timestamp'] = datetime.now()

                if 'completion_timestamp' in task_data and isinstance(task_data['completion_timestamp'], str):
                    try:
                        task_data['completion_timestamp'] = datetime.strptime(task_data['completion_timestamp'], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        task_data['completion_timestamp'] = None
                else:
                    task_data['completion_timestamp'] = None

                add_task(
                    task_data.get('text', ''),
                    task_data.get('notes', ''),
                    task_data['timestamp'],
                    task_data.get('status', 'pending'),
                    task_data['completion_timestamp'],
                    task_data.get('priority', 'Medium')
                )
            os.remove(TASKS_FILE)
            print("Task migration complete and local tasks.json removed.")
        except Exception as e:
            print(f"Error during task migration: {e}")
    else:
        print("No local tasks.json found for migration.")




# API Routes

@app.get("/")
async def read_root():
    """Serve the Vue.js frontend"""
    return FileResponse('frontend/dist/index.html')

# Text Readability Editor endpoints
@app.post("/api/text-readability", response_model=TextReadabilityResponse)
async def process_text_readability(request: TextReadabilityRequest):
    """Process text for readability improvement"""
    if not change_reading_score:
        raise HTTPException(status_code=501, detail="Text readability feature is not available")
    
    try:
        start_time = time.time()
        processed_text = change_reading_score(request.text, request.target_score)
        end_time = time.time()
        
        current_score = textstat.flesch_reading_ease(processed_text)
        grade_level = textstat.flesch_kincaid_grade(processed_text)
        processing_time = end_time - start_time
        
        return TextReadabilityResponse(
            processed_text=processed_text,
            current_score=current_score,
            grade_level=grade_level,
            processing_time=processing_time
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing text: {str(e)}")

# Task Tracker endpoints
@app.get("/api/tasks", response_model=List[Task])
async def get_tasks_api():
    """Get all tasks"""
    try:
        tasks_data = get_tasks()
        return [Task(**task) for task in tasks_data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving tasks: {str(e)}")

@app.post("/api/tasks", response_model=Task)
async def create_task_api(task_in: TaskIn):
    """Create a new task"""
    try:
        task_id = add_task(
            task_in.text,
            task_in.notes,
            datetime.now(), # timestamp
            task_in.status,
            task_in.completion_timestamp,
            task_in.priority
        )
        # Retrieve the newly created task to return its full data including _id
        created_task = next((t for t in get_tasks() if str(t['_id']) == task_id), None)
        if not created_task:
            raise HTTPException(status_code=500, detail="Task creation failed")
        return Task(**created_task)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating task: {str(e)}")

@app.put("/api/tasks/{task_id}", response_model=Task)
async def update_task_api(task_id: str, task_in: TaskIn):
    """Update a task"""
    try:
        db_update_task(
            task_id,
            task_in.text,
            task_in.notes,
            datetime.now(), # Update timestamp on modification
            task_in.status,
            task_in.completion_timestamp,
            task_in.priority
        )
        updated_task = next((t for t in get_tasks() if str(t['_id']) == task_id), None)
        if not updated_task:
            raise HTTPException(status_code=404, detail="Task not found")
        return Task(**updated_task)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating task: {str(e)}")

@app.delete("/api/tasks/{task_id}")
async def delete_task_api(task_id: str):
    """Delete a task"""
    try:
        db_delete_task(task_id)
        return {"message": "Task deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting task: {str(e)}")

# Notes endpoints
@app.get("/api/notes", response_model=List[Note])
async def list_notes_api(search_query: Optional[str] = None, tags: Optional[str] = None):
    """Get all notes, with optional search and tag filters"""
    try:
        notes_data = get_notes(search_query=search_query, tags=tags)
        return [Note(**note) for note in notes_data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving notes: {str(e)}")

@app.post("/api/notes", response_model=Note)
async def create_note_api(note_in: NoteIn):
    """Create a new note"""
    try:
        note_id = add_note(note_in.title, note_in.content, note_in.tags)
        created_note = get_note(note_id)
        if not created_note:
            raise HTTPException(status_code=500, detail="Note creation failed")
        return Note(**created_note)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating note: {str(e)}")

@app.get("/api/notes/{note_id}", response_model=Note)
async def get_note_api(note_id: str):
    """Get a single note by ID"""
    try:
        note = get_note(note_id)
        if not note:
            raise HTTPException(status_code=404, detail="Note not found")
        return Note(**note)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving note: {str(e)}")

@app.put("/api/notes/{note_id}", response_model=Note)
async def update_note_api(note_id: str, note_in: NoteIn):
    """Update an existing note"""
    try:
        update_note(note_id, note_in.title, note_in.content, note_in.tags)
        updated_note = get_note(note_id)
        if not updated_note:
            raise HTTPException(status_code=404, detail="Note not found after update")
        return Note(**updated_note)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating note: {str(e)}")

@app.delete("/api/notes/{note_id}")
async def delete_note_api(note_id: str):
    """Delete a note by ID"""
    try:
        delete_note(note_id)
        return {"message": "Note deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting note: {str(e)}")

@app.get("/api/notes/tags")
async def get_all_tags_api():
    """Get all unique tags from notes"""
    try:
        tags = get_all_tags()
        return tags
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving tags: {str(e)}")

# Deep Conversations endpoints
@app.post("/api/conversations/start", response_model=ConversationResponse)
async def start_conversation_api(request: ConversationRequest):
    """Start a new conversation between two LLM agents"""
    try:
        result = start_conversation(
            seed_question=request.seed_question,
            num_exchanges=request.num_exchanges,
            use_gemini=request.use_gemini
        )
        
        if result['success']:
            conversation_messages = [
                ConversationMessage(**msg) for msg in result['conversation']
            ]
            return ConversationResponse(
                success=True,
                conversation_id=result['conversation_id'],
                seed_question=result['seed_question'],
                num_exchanges=result['num_exchanges'],
                conversation=conversation_messages,
                created_at=result['created_at']
            )
        else:
            return ConversationResponse(
                success=False,
                error=result['error']
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting conversation: {str(e)}")

# Static files for Vue.js frontend
app.mount("/assets", StaticFiles(directory="frontend/dist/assets"), name="assets")

# Serve Vue.js app for all other routes (SPA routing)
@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    """Serve the Vue.js SPA for all routes"""
    return FileResponse('frontend/dist/index.html')

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000) 