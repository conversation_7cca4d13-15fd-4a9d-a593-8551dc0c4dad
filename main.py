"""
Productivity Tools API

A FastAPI application providing various productivity tools including:
- Text readability analysis and improvement
- Task management (CRUD operations)
- Note management with search and tagging
- LLM conversation simulation

This refactored version uses modular routers for clean separation of concerns.
"""

import sys
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# Add the current directory to sys.path to import from agents and api
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.routers import text_readability, tasks, notes, conversations
from api.utils import migrate_tasks_from_json


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Handles startup and shutdown events.
    """
    # Startup: Migrate tasks from JSON to MongoDB if needed
    migrate_tasks_from_json()
    yield
    # Shutdown: cleanup if needed (none currently required)
    # The app parameter is required by FastAPI's lifespan protocol


# Initialize FastAPI application
app = FastAPI(
    title="Productivity Tools API",
    version="1.0.0",
    description="A comprehensive API for productivity tools including text analysis, task management, notes, and AI conversations.",
    lifespan=lifespan
)

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers for different modules
app.include_router(text_readability.router)
app.include_router(tasks.router)
app.include_router(notes.router)  
app.include_router(conversations.router)


# Frontend serving routes
@app.get("/")
async def serve_frontend():
    """Serve the Vue.js frontend application."""
    return FileResponse('frontend/dist/index.html')


# Static files for Vue.js frontend
app.mount("/assets", StaticFiles(directory="frontend/dist/assets"), name="assets")


# Catch-all route for SPA routing
@app.get("/{full_path:path}")
async def serve_spa():
    """Serve the Vue.js SPA for all unmatched routes."""
    return FileResponse('frontend/dist/index.html')


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)