<template>
  <article @click="$emit('click')">
    <header>
      <h3>{{ title }}</h3>
    </header>
    <p>{{ description }}</p>
  </article>
</template>

<script>
export default {
  name: 'Tool<PERSON><PERSON>',
  emits: ['click'],
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    route: {
      type: String,
      required: true
    }
  }
}
</script>

<style scoped>
/* No scoped styles needed as Pico.css handles most of it */
</style>