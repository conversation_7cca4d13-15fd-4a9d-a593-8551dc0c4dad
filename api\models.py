"""
Shared Pydantic models for the Productivity Tools API.
"""

from pydantic import BaseModel, Field
from pydantic_core import core_schema
from typing import List, Optional, Any
from datetime import datetime
from bson import ObjectId


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic models."""
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate_object_id),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def validate_object_id(cls, value: str) -> ObjectId:
        """Validate ObjectId format."""
        if not ObjectId.is_valid(value):
            raise ValueError("Invalid ObjectId format")
        return ObjectId(value)


# Text Readability Models
class TextReadabilityRequest(BaseModel):
    """Request model for text readability processing."""
    text: str
    target_score: int


class TextReadabilityResponse(BaseModel):
    """Response model for text readability processing."""
    processed_text: str
    current_score: float
    grade_level: float
    processing_time: float


# Task Models
class TaskBase(BaseModel):
    """Base task model with common fields."""
    text: str
    notes: str = ""
    status: str = "pending"
    priority: str = "Medium"


class TaskCreate(TaskBase):
    """Model for creating a new task."""
    completion_timestamp: Optional[datetime] = None


class TaskUpdate(TaskBase):
    """Model for updating an existing task."""
    completion_timestamp: Optional[datetime] = None


class Task(TaskBase):
    """Complete task model with database fields."""
    id: Optional[PyObjectId] = Field(alias="_id")
    timestamp: datetime = Field(default_factory=datetime.now)
    completion_timestamp: Optional[datetime] = None

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


# Note Models  
class NoteBase(BaseModel):
    """Base note model with common fields."""
    title: str
    content: str


class NoteCreate(NoteBase):
    """Model for creating a new note."""
    tags: Optional[str] = ""


class NoteUpdate(NoteBase):
    """Model for updating an existing note."""
    tags: Optional[str] = ""


class Note(NoteBase):
    """Complete note model with database fields."""
    id: Optional[PyObjectId] = Field(alias="_id")
    tags: Optional[List[str]] = []
    created_at: datetime = Field(default_factory=datetime.now)
    last_modified: datetime = Field(default_factory=datetime.now)

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


# Conversation Models
class ConversationStartRequest(BaseModel):
    """Request model for starting a new conversation."""
    seed_question: str
    use_gemini: bool = True


class ConversationContinueRequest(BaseModel):
    """Request model for continuing an existing conversation."""
    session_id: str


class ConversationMessage(BaseModel):
    """Individual message in a conversation."""
    agent_name: str
    message: str
    timestamp: str
    exchange_number: int
    session_id: Optional[str] = None
    is_error: Optional[bool] = None


class ConversationStartResponse(BaseModel):
    """Response model for starting a conversation."""
    success: bool
    session_id: Optional[str] = None
    message: Optional[ConversationMessage] = None
    next_agent: Optional[str] = None
    error: Optional[str] = None


class ConversationContinueResponse(BaseModel):
    """Response model for continuing a conversation."""
    success: bool
    session_id: Optional[str] = None
    message: Optional[ConversationMessage] = None
    next_agent: Optional[str] = None
    conversation_length: Optional[int] = None
    has_error: Optional[bool] = None
    error: Optional[str] = None


class ConversationSummaryResponse(BaseModel):
    """Response model for getting conversation summary."""
    success: bool
    session_id: Optional[str] = None
    seed_question: Optional[str] = None
    conversation: List[ConversationMessage] = []
    created_at: Optional[str] = None
    message_count: Optional[int] = None
    next_agent: Optional[str] = None
    error: Optional[str] = None


# Legacy conversation models for backward compatibility
class ConversationRequest(BaseModel):
    """Legacy request model for bulk conversation generation."""
    seed_question: str
    num_exchanges: int = 5
    use_gemini: bool = True


class ConversationResponse(BaseModel):
    """Legacy response model for bulk conversation generation."""
    success: bool
    conversation_id: Optional[str] = None
    seed_question: Optional[str] = None
    num_exchanges: Optional[int] = None
    conversation: List[ConversationMessage] = []
    created_at: Optional[str] = None
    error: Optional[str] = None


# Generic Response Models
class SuccessResponse(BaseModel):
    """Generic success response."""
    message: str


class ErrorResponse(BaseModel):
    """Generic error response."""
    error: str
    detail: Optional[str] = None