import os
from crewai import Agent, Task, Crew, Process
from .llms import ollama_llm

# Define the Text-Rewriting Agent
rewriting_agent = Agent(
    role="Text Rewriter",
    goal="Rewrite text to meet a specific Flesch reading-ease score, making it more readable and clear.",
    backstory=(
        "You are an expert in linguistics and writing. You have a deep understanding of readability metrics "
        "and can skillfully adjust the complexity of text without losing its original meaning. You are adept at "
        "simplifying sentences, choosing more common words, and restructuring paragraphs to improve clarity."
    ),
    llm=ollama_llm,
    allow_delegation=False,
    verbose=True
)

# Define the Text-Processing Task
rewriting_task = Task(
    description=(
        "Analyze the following text: '{text}' and rewrite it to achieve a Flesch reading-ease score of approximately {score}. "
        "The original meaning and key information must be preserved. Focus on improving sentence structure and word choice."
    ),
    expected_output=(
        "A revised version of the text that is easier to read and has a Flesch reading-ease score around {score}. "
        "The output should only contain the rewritten text."
    ),
    agent=rewriting_agent
)

def change_reading_score(text, score):
    # Instantiate the crew with a sequential process
    text_crew = Crew(
        agents=[rewriting_agent],
        tasks=[rewriting_task],
        process=Process.sequential
    )

    # Kick off the crew's work
    result = text_crew.kickoff(inputs={"text": text, "score" :  score})
    return str(result)

# Example Usage
if __name__ == "__main__":
    # This is a sample text with a low readability score
    sample_text = (
        "The proliferation of autonomous systems necessitates a paradigm shift in regulatory frameworks, focusing on "
        "algorithmic accountability and ethical considerations. These systems, characterized by their complex, often "
        "opaque decision-making processes, present unprecedented challenges to traditional legal and moral doctrines, "
        "demanding a multi-faceted approach to governance that balances innovation with public safety."
    )

    print("Original Text:")
    print(sample_text)
    print("\n--- Running Crew ---")

    # Instantiate the crew with a sequential process
    text_crew = Crew(
        agents=[rewriting_agent],
        tasks=[rewriting_task],
        process=Process.sequential
    )

    # Kick off the crew's work
    result = text_crew.kickoff(inputs={"text": sample_text, "score" :  65})

    print("\n--- Rewritten Text ---")
    print(result)