<template>
  <div class="container">
    <hgroup>
      <h1>Text Readability Editor</h1>
      <p>
        Improve your text readability using AI-powered analysis and suggestions
      </p>
    </hgroup>
    <div class="grid">
      <div class="col">
        <label for="input-text">Input Text</label>
        <textarea
          v-model="inputText"
          id="input-text"
          placeholder="Enter your text here..."
          rows="15"
        ></textarea>
        <label for="target-score">Target Flesch Score</label>
        <input
          v-model.number="targetScore"
          type="number"
          id="target-score"
          min="1"
          max="100"
          placeholder="60"
        />
        <button @click="processText" :disabled="!inputText || isProcessing">
          {{ isProcessing ? "Processing..." : "Process Text" }}
        </button>
      </div>

      <div class="col">
        <label for="output-text">Processed Text <span v-if="result">| Current Score : {{ result.currentScore?.toFixed(1) || "N/A" }}</span></label>
        <textarea
          v-model="outputText"
          id="output-text"
          placeholder="Processed text will appear here..."
          rows="15"
          readonly
        ></textarea>
      </div>
    </div>
    <p v-if="error" class="error">{{ error }}</p>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "TextReadabilityEditor",
  data() {
    return {
      inputText: "",
      outputText: "",
      targetScore: 60,
      isProcessing: false,
      result: null,
      error: null,
    };
  },
  methods: {
    async processText() {
      if (!this.inputText.trim()) {
        this.error = "Please enter some text to process";
        return;
      }

      this.isProcessing = true;
      this.error = null;

      try {
        const response = await axios.post("/api/text-readability", {
          text: this.inputText,
          target_score: this.targetScore,
        });

        this.outputText = response.data.processed_text;
        this.result = {
          currentScore: response.data.current_score,
          gradeLevel: response.data.grade_level,
          processingTime: response.data.processing_time,
        };
      } catch (error) {
        this.error =
          "Error processing text: " +
          (error.response?.data?.detail || error.message);
      } finally {
        this.isProcessing = false;
      }
    },
  },
};
</script>

<style scoped>
.error {
  color: var(--pico-color-red-500);
}
</style>
