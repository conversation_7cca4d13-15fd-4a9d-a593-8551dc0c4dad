<template>
  <label class="switch">
    <!-- Sun -->
    <svg
      v-if="currentTheme !== 'dark'"
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width="24"
      height="24"
      viewBox="0 0 24 24"
    >
      <path
        d="M12 5A7 7 0 1012 19 7 7 0 1012 5zM12 3c-.552 0-1-.448-1-1V1c0-.552.448-1 1-1s1 .448 1 1v1C13 2.552 12.552 3 12 3zM12 24c-.552 0-1-.447-1-1v-1c0-.553.448-1 1-1s1 .447 1 1v1C13 23.553 12.552 24 12 24zM23 13h-1c-.553 0-1-.448-1-1s.447-1 1-1h1c.553 0 1 .448 1 1S23.553 13 23 13zM2 13H1c-.552 0-1-.448-1-1s.448-1 1-1h1c.552 0 1 .448 1 1S2.552 13 2 13zM19.8 20.8c-.256 0-.512-.098-.707-.293l-.7-.7c-.391-.391-.391-1.023 0-1.414s1.023-.391 1.414 0l.7.7c.391.391.391 1.023 0 1.414C20.312 20.702 20.056 20.8 19.8 20.8zM4.9 5.9c-.256 0-.512-.098-.707-.293l-.7-.7c-.391-.391-.391-1.023 0-1.414s1.023-.391 1.414 0l.7.7c.391.391.391 1.023 0 1.414C5.412 5.802 5.156 5.9 4.9 5.9zM19.1 5.9c-.256 0-.512-.098-.707-.293-.391-.391-.391-1.024 0-1.415l.7-.7c.391-.39 1.023-.39 1.414 0s.391 1.024 0 1.415l-.7.7C19.611 5.802 19.355 5.9 19.1 5.9zM4.2 20.8c-.256 0-.512-.098-.707-.293-.391-.39-.391-1.023 0-1.414l.7-.7c.391-.391 1.024-.391 1.415 0 .391.39.391 1.023 0 1.414l-.7.7C4.712 20.702 4.456 20.8 4.2 20.8z"
      ></path>
    </svg>

    <!--Moon-->
    <svg
      v-if="currentTheme === 'dark'"
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="currentColor"
      class="bi bi-moon-fill"
      viewBox="0 0 16 16"
    >
      <path
        d="M6 .278a.77.77 0 0 1 .08.858 7.2 7.2 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277q.792-.001 1.533-.16a.79.79 0 0 1 .81.316.73.73 0 0 1-.031.893A8.35 8.35 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.75.75 0 0 1 6 .278"
      />
    </svg>
    <input
      type="checkbox"
      @change="toggleTheme"
      :checked="currentTheme === 'dark'"
    />
  </label>
</template>

<script>
export default {
  name: "ThemeToggle",
  data() {
    return {
      currentTheme: "light",
    };
  },
  methods: {
    toggleTheme() {
      const newTheme = this.currentTheme === "light" ? "dark" : "light";
      document.documentElement.setAttribute("data-theme", newTheme);
      localStorage.setItem("theme", newTheme);
      this.currentTheme = newTheme;
    },
    loadTheme() {
      const savedTheme = localStorage.getItem("theme");
      if (savedTheme) {
        document.documentElement.setAttribute("data-theme", savedTheme);
        this.currentTheme = savedTheme;
      }
    },
  },
  mounted() {
    this.loadTheme();
  },
};
</script>

<style scoped>
/* No scoped styles needed for ThemeToggle.vue as Pico.css handles most of it */
</style>
