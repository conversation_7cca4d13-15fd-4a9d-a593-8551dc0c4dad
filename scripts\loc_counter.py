#!/usr/bin/env python3
"""
Lines of Code Counter
Counts lines of code in current folder and subfolders, exports to CSV
"""

import os
import sys
import re
import pandas as pd
from collections import defaultdict
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import add_gitlab_loc_bulk

program_start_timestamp = datetime.now().strftime('%Y%m%d')

class LOCCounter:
    def __init__(self, root_path='.', output_file=None):
        self.root_path = os.path.abspath(root_path)
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.output_file = f'loc_report_{timestamp}.csv'
        else:
            self.output_file = output_file
        self.results = []
        self.summary = defaultdict(lambda: {'files': 0, 'total_lines': 0, 'code_lines': 0, 'blank_lines': 0, 'comment_lines': 0})
        
        # File extensions for code files
        self.code_extensions = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.jsx': 'JavaScript React',
            '.ts': 'TypeScript',
            '.tsx': 'TypeScript React',
            '.java': 'Java',
            '.c': 'C',
            '.cpp': 'C++',
            '.cc': 'C++',
            '.cxx': 'C++',
            '.h': 'C/C++ Header',
            '.hpp': 'C++ Header',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.r': 'R',
            '.m': 'Objective-C',
            '.mm': 'Objective-C++',
            '.pl': 'Perl',
            '.sh': 'Shell',
            '.bash': 'Bash',
            '.zsh': 'Zsh',
            '.fish': 'Fish',
            '.ps1': 'PowerShell',
            '.bat': 'Batch',
            '.cmd': 'Command',
            '.html': 'HTML',
            '.htm': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.less': 'Less',
            '.xml': 'XML',
            '.json': 'JSON',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.sql': 'SQL',
            '.md': 'Markdown',
            '.rst': 'reStructuredText',
            '.tex': 'LaTeX',
            '.vue': 'Vue',
            '.svelte': 'Svelte',
            '.dart': 'Dart',
            '.lua': 'Lua',
            '.vim': 'Vim Script',
            '.dockerfile': 'Dockerfile',
            '.makefile': 'Makefile',
            '.cmake': 'CMake',
            '.gradle': 'Gradle'
        }
        
        # Simplified comment patterns
        self.comment_patterns = [
            r'^\s*//.*',        # C-style single line
            r'^\s*#.*',         # Shell, Python, Ruby
            r'^\s*--.*',        # SQL, Haskell
            r'^\s*%.*',         # LaTeX
            r'^\s*/\*.*\*/\s*$', # C-style single line /* */
            r'^\s*<!--.*-->\s*$' # HTML single line comments
        ]
    
    def is_code_file(self, file_path):
        """Check if file is a code file based on extension"""
        _, ext = os.path.splitext(file_path.lower())
        
        # Special case for files without extensions
        filename = os.path.basename(file_path).lower()
        if filename in ['dockerfile', 'makefile', 'rakefile', 'gemfile']:
            return True
        
        return ext in self.code_extensions
    
    def get_language(self, file_path):
        """Get programming language from file extension"""
        _, ext = os.path.splitext(file_path.lower())
        filename = os.path.basename(file_path).lower()
        
        # Special cases
        if filename in ['dockerfile']:
            return 'Dockerfile'
        elif filename in ['makefile', 'rakefile']:
            return 'Makefile'
        elif filename in ['gemfile']:
            return 'Ruby'
        
        return self.code_extensions.get(ext, 'Unknown')
    
    def is_comment_line(self, line):
        """Check if line is a comment"""
        line = line.strip()
        return any(re.match(pattern, line) for pattern in self.comment_patterns)
    
    def count_lines_in_file(self, file_path):
        """Count different types of lines in a file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return None
        
        total_lines = len(lines)
        blank_lines = sum(1 for line in lines if not line.strip())
        comment_lines = sum(1 for line in lines if line.strip() and self.is_comment_line(line))
        code_lines = total_lines - blank_lines - comment_lines
        
        return {
            'total_lines': total_lines,
            'code_lines': code_lines,
            'blank_lines': blank_lines,
            'comment_lines': comment_lines
        }
    
    def scan_directory(self):
        """Scan directory recursively for code files"""
        print(f"Scanning directory: {self.root_path}")
        
        total_files = 0
        processed_files = 0
        project_name = os.path.basename(self.root_path)
        
        for root, dirs, files in os.walk(self.root_path):
            # Skip common non-code directories
            dirs[:] = [d for d in dirs if d not in ['.git', '.svn', '.hg', 'node_modules', '__pycache__', 
                                                   '.pytest_cache', 'venv', 'env', '.venv', 'build', 
                                                   'dist', 'target', '.idea', '.vscode']]
            
            for file in files:
                file_path = os.path.join(root, file)

                
                
                if self.is_code_file(file_path):
                    total_files += 1
                    
                    relative_path = os.path.relpath(file_path, self.root_path)
                    language = self.get_language(file_path)
                    
                    line_count = self.count_lines_in_file(file_path)
                    if line_count:
                        processed_files += 1
                        
                        self.results.append({
                            'project_name': project_name,
                            'file_path': relative_path,
                            'language': language,
                            'total_lines': line_count['total_lines'],
                            'code_lines': line_count['code_lines'],
                            'comment_lines': line_count['comment_lines'],
                            'blank_lines': line_count['blank_lines'],
                            'file_size': os.path.getsize(file_path),
                            'timestamp': program_start_timestamp
                        })
                        
                        # Update summary
                        self.summary[language]['files'] += 1
                        self.summary[language]['total_lines'] += line_count['total_lines']
                        self.summary[language]['code_lines'] += line_count['code_lines']
                        self.summary[language]['comment_lines'] += line_count['comment_lines']
                        self.summary[language]['blank_lines'] += line_count['blank_lines']
                        
                        if processed_files % 100 == 0:
                            print(f"Processed {processed_files} files...")
        
        print(f"Completed! Processed {processed_files} out of {total_files} code files.")        
        # Consolidate results by project and language
        consolidated_results = defaultdict(lambda: defaultdict(lambda: {
            'files': 0, 'total_lines': 0, 'code_lines': 0, 
            'comment_lines': 0, 'blank_lines': 0, 'file_size': 0
        }))
        
        for result in self.results:
            proj = result['project_name']
            lang = result['language']
            consolidated_results[proj][lang]['files'] += 1
            consolidated_results[proj][lang]['total_lines'] += result['total_lines']
            consolidated_results[proj][lang]['code_lines'] += result['code_lines']
            consolidated_results[proj][lang]['comment_lines'] += result['comment_lines']
            consolidated_results[proj][lang]['blank_lines'] += result['blank_lines']
            consolidated_results[proj][lang]['file_size'] += result['file_size']
        
        add_gitlab_loc_bulk(consolidated_results)
        
        return processed_files
    
    def export_to_csv(self):
        """Export results to CSV file using pandas"""
        if not self.results:
            print("No data to export.")
            return
            
        # Create DataFrame from results
        df = pd.DataFrame(self.results)
        df = df.sort_values(['language', 'file_path'])
        
        # Create summary DataFrame
        summary_data = []
        for language, stats in sorted(self.summary.items(), key=lambda x: x[1]['total_lines'], reverse=True):
            summary_data.append({
                'Language': language,
                'Files': stats['files'],
                'Total Lines': stats['total_lines'],
                'Code Lines': stats['code_lines'],
                'Comment Lines': stats['comment_lines'],
                'Blank Lines': stats['blank_lines']
            })
        
        summary_df = pd.DataFrame(summary_data)
        
        # Add totals row
        totals = {
            'Language': 'TOTAL',
            'Files': summary_df['Files'].sum(),
            'Total Lines': summary_df['Total Lines'].sum(),
            'Code Lines': summary_df['Code Lines'].sum(),
            'Comment Lines': summary_df['Comment Lines'].sum(),
            'Blank Lines': summary_df['Blank Lines'].sum()
        }
        summary_df = pd.concat([summary_df, pd.DataFrame([totals])], ignore_index=True)
        
        # Export to CSV with multiple sheets simulation
        with open(self.output_file, 'w', newline='', encoding='utf-8') as f:
            f.write("# DETAILED RESULTS\n")
            df.to_csv(f, index=False)
            f.write("\n# SUMMARY BY LANGUAGE\n")
            summary_df.to_csv(f, index=False)
    
    def print_summary(self):
        """Print summary to console"""
        print(f"\n=== Lines of Code Summary ===")
        print(f"Scan completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Root directory: {self.root_path}")
        print()
        
        if not self.summary:
            print("No code files found.")
            return
        
        # Sort by total lines
        sorted_summary = sorted(self.summary.items(), key=lambda x: x[1]['total_lines'], reverse=True)
        
        print(f"{'Language':<20} {'Files':<8} {'Total':<10} {'Code':<10} {'Comments':<10} {'Blank':<10}")
        print("-" * 78)
        
        total_files = 0
        total_lines = 0
        total_code = 0
        total_comments = 0
        total_blanks = 0
        
        for language, stats in sorted_summary:
            print(f"{language:<20} {stats['files']:<8} {stats['total_lines']:<10} "
                  f"{stats['code_lines']:<10} {stats['comment_lines']:<10} {stats['blank_lines']:<10}")
            
            total_files += stats['files']
            total_lines += stats['total_lines']
            total_code += stats['code_lines']
            total_comments += stats['comment_lines']
            total_blanks += stats['blank_lines']
        
        print("-" * 78)
        print(f"{'TOTAL':<20} {total_files:<8} {total_lines:<10} {total_code:<10} {total_comments:<10} {total_blanks:<10}")
        print()
        print(f"Results exported to: {self.output_file}")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Count lines of code in directory')
    parser.add_argument('--path', default='.', help='Directory to scan (default: current directory)')
    parser.add_argument('--output', help='Output CSV file (default: loc_report_YYYYMMDD_HHMMSS.csv)')
    
    args = parser.parse_args()
    
    counter = LOCCounter(args.path, args.output)
    
    files_processed = counter.scan_directory()
    if files_processed > 0:
        counter.export_to_csv()
        counter.print_summary()
    else:
        print("No code files found to process.")


if __name__ == "__main__":
    main()