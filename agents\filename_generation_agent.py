
from crewai import Agent, Task, Crew
from .llms import ollama_llm

# Define the Filename Generation Agent
filename_agent = Agent(
    role='File Naming Specialist',
    goal='Generate a concise, meaningful, and safe file name from the provided content.',
    backstory=(
        'You are an expert in organizing digital content. Your skill is to quickly understand'
        'the essence of a text and create a descriptive, snake_cased file name that is easy to'
        'understand and safe for all operating systems.'
    ),
    verbose=True,
    allow_delegation=False,
    llm=ollama_llm
)

def generate_filename(content):
    """
    Initializes and runs the CrewAI crew to generate a filename for the given content.
    """
    # Define the Task for the Agent
    filename_task = Task(
        description=f'Analyze the following content and generate a meaningful file name for it. Content:\n\n{content}',
        expected_output='A single, snake_cased file name ending with .md. For example: "meeting_notes_project_alpha.md"',
        agent=filename_agent
    )

    # Create the Crew
    filename_crew = Crew(
        agents=[filename_agent],
        tasks=[filename_task],
        verbose=True
    )

    # Execute the Crew and get the result
    result = filename_crew.kickoff()
    return str(result)

if __name__ == '__main__':
    sample_content = """
    This is a test note about the new feature for project alpha.
    We discussed the implementation details and the timeline.
    The feature should be completed by the end of the next sprint.
    """
    
    filename = generate_filename(sample_content)
    print(f"\nGenerated Filename: {filename}")
