import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import TextReadabilityEditor from '../views/TextReadabilityEditor.vue'
import TaskTracker from '../views/TaskTracker.vue'
import PasswordGenerator from '../views/PasswordGenerator.vue'
import Notepad from '../views/Notepad.vue'
import DeepConversations from '../views/DeepConversations.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/text-readability-editor',
    name: 'TextReadabilityEditor',
    component: TextReadabilityEditor
  },
  {
    path: '/task-tracker',
    name: 'TaskTracker',
    component: TaskTracker
  },
  {
    path: '/password-generator',
    name: 'PasswordGenerator',
    component: PasswordGenerator
  },
  {
    path: '/notepad',
    name: 'Notepad',
    component: Notepad
  },
  {
    path: '/deep-conversations',
    name: 'DeepConversations',
    component: DeepConversations
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 