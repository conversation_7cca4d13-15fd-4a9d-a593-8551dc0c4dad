"""
Utility functions for the Productivity Tools API.
"""

import os
import json
from datetime import datetime
from typing import Dict, Any
from fastapi import H<PERSON><PERSON>Exception
from database import add_task


def migrate_tasks_from_json(tasks_file: str = "tasks.json") -> None:
    """
    Migrate tasks from JSON file to MongoDB.
    
    Args:
        tasks_file: Path to the tasks JSON file
    """
    if not os.path.exists(tasks_file):
        print("No local tasks.json found for migration.")
        return
        
    print(f"Migrating tasks from {tasks_file} to MongoDB...")
    
    try:
        with open(tasks_file, 'r') as f:
            local_tasks = json.load(f)
        
        for task_data in local_tasks:
            # Ensure timestamp is a datetime object
            timestamp = _parse_datetime(
                task_data.get('timestamp'), 
                default=datetime.now()
            )
            
            # Ensure completion_timestamp is a datetime object or None
            completion_timestamp = _parse_datetime(
                task_data.get('completion_timestamp')
            )
            
            add_task(
                task_data.get('text', ''),
                task_data.get('notes', ''),
                timestamp,
                task_data.get('status', 'pending'),
                completion_timestamp,
                task_data.get('priority', 'Medium')
            )
            
        os.remove(tasks_file)
        print("Task migration complete and local tasks.json removed.")
        
    except Exception as e:
        print(f"Error during task migration: {e}")


def _parse_datetime(date_str: Any, default: datetime = None) -> datetime:
    """
    Parse datetime string to datetime object.
    
    Args:
        date_str: String representation of datetime
        default: Default datetime if parsing fails
        
    Returns:
        Parsed datetime object or default
    """
    if not date_str or not isinstance(date_str, str):
        return default
        
    try:
        return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        return default if default is not None else datetime.now()


def handle_database_error(operation: str, error: Exception) -> HTTPException:
    """
    Create standardized HTTP exception for database errors.
    
    Args:
        operation: Description of the operation that failed
        error: The original exception
        
    Returns:
        HTTPException with appropriate status code and message
    """
    error_message = f"Error {operation}: {str(error)}"
    
    # Map specific error types to HTTP status codes
    if "not found" in str(error).lower():
        return HTTPException(status_code=404, detail=error_message)
    elif "validation" in str(error).lower():
        return HTTPException(status_code=400, detail=error_message)
    else:
        return HTTPException(status_code=500, detail=error_message)


def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """
    Validate that required fields are present in data.
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Raises:
        HTTPException: If required fields are missing
    """
    missing_fields = [field for field in required_fields if not data.get(field)]
    
    if missing_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Missing required fields: {', '.join(missing_fields)}"
        )