from crewai import Agent, Task, Crew
from .llms import ollama_llm, gemini_llm
from typing import List, Dict
import time
from datetime import datetime

class ConversationSimulator:
    def __init__(self, use_gemini=True):
        self.llm = gemini_llm if use_gemini else ollama_llm
        
        # Create two distinct agents with different personalities
        self.agent_a = Agent(
            role="Philosophical Thinker",
            goal="Engage in deep, thoughtful conversations by asking probing questions and sharing insights",
            backstory="""You are a curious philosopher who loves exploring big ideas. You tend to ask 
            thought-provoking questions and share personal reflections. You're empathetic and always 
            try to understand different perspectives.""",
            llm=self.llm,
            verbose=False,
            allow_delegation=False,
            max_iter=1
        )
        
        self.agent_b = Agent(
            role="Practical Analyst",
            goal="Provide practical perspectives and analyze ideas from multiple angles",
            backstory="""You are a practical thinker who excels at breaking down complex ideas into 
            actionable insights. You're analytical, enjoy examining evidence, and often play devil's 
            advocate to explore all sides of an issue.""",
            llm=self.llm,
            verbose=False,
            allow_delegation=False,
            max_iter=1
        )
    
    def simulate_conversation(self, seed_question: str, num_exchanges: int = 5) -> List[Dict]:
        """
        Simulate a conversation between two agents
        Returns a list of conversation exchanges
        """
        conversation = []
        current_topic = seed_question
        
        # Start with Agent A responding to the seed question
        current_agent = self.agent_a
        other_agent = self.agent_b
        
        for exchange in range(num_exchanges * 2):  # Each exchange involves both agents
            try:
                if exchange == 0:
                    # First response to seed question
                    prompt = f"Respond thoughtfully to this question or topic: '{current_topic}'. Share your perspective and ask a follow-up question to continue the conversation. Keep your response to 2 paragraphs maximum."
                else:
                    # Subsequent responses
                    last_message = conversation[-1]['message']
                    prompt = f"Continue this deep conversation by responding to: '{last_message}'. Build on the ideas shared and ask a thoughtful follow-up question. Keep your response to 2 paragraphs maximum."
                
                task = Task(
                    description=prompt,
                    agent=current_agent,
                    expected_output="A thoughtful response that builds on the conversation and includes a follow-up question"
                )
                
                crew = Crew(
                    agents=[current_agent],
                    tasks=[task],
                    verbose=False
                )
                
                result = crew.kickoff()
                
                conversation.append({
                    'agent_name': current_agent.role,
                    'message': str(result),
                    'timestamp': datetime.now().isoformat(),
                    'exchange_number': exchange + 1
                })
                
                # Switch agents
                current_agent, other_agent = other_agent, current_agent
                
                # Short delay to prevent rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"Error in conversation exchange {exchange + 1}: {str(e)}")
                conversation.append({
                    'agent_name': current_agent.role,
                    'message': f"I'm having trouble continuing the conversation. Let me think about this differently... {str(e)}",
                    'timestamp': datetime.now().isoformat(),
                    'exchange_number': exchange + 1
                })
                current_agent, other_agent = other_agent, current_agent
        
        return conversation

def start_conversation(seed_question: str, num_exchanges: int = 5, use_gemini: bool = True) -> Dict:
    """
    Start a new conversation simulation
    """
    try:
        simulator = ConversationSimulator(use_gemini=use_gemini)
        conversation = simulator.simulate_conversation(seed_question, num_exchanges)
        
        return {
            'success': True,
            'conversation_id': f"conv_{int(time.time())}",
            'seed_question': seed_question,
            'num_exchanges': num_exchanges,
            'conversation': conversation,
            'created_at': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'conversation': []
        }