<template>
  <div class="container">
    <nav>
      <ul>
        <li>
          <hgroup>
            <h1>Markdown Notepad</h1>
            <p>Create and manage your notes with markdown support</p>
          </hgroup>
        </li>
      </ul>
      <ul>
        <li>
          <button @click="createNewNote" class="outline">New Note</button>
        </li>
      </ul>
    </nav>
    <div class="notes-grid">
      <aside class="sidebar">
        <nav>
          <ul class="notes-items">
            <li>
              <input
                v-model="searchQuery"
                type="search"
                placeholder="Search notes..."
              />
            </li>
            <li
              v-for="note in filteredNotes"
              :key="note._id"
              @click="selectNote(note)"
              :class="{ active: currentNote?._id === note._id }"
            >
              <a class="notes-list-item" href="#">
                <strong>{{ note.title }}</strong>
              </a>
            </li>
            <li v-if="filteredNotes.length === 0">
              <p>
                {{
                  searchQuery
                    ? "No notes match your search."
                    : "No notes yet. Create your first note!"
                }}
              </p>
            </li>
          </ul>
        </nav>
      </aside>
      <article class="content">
        <input
          v-if="currentNote"
          v-model="currentNote.title"
          type="text"
          placeholder="Note title..."
          @input="updateNote"
        />
        <nav>
          <ul>
            <li>
              <a
                href="#"
                :class="{ active: activeTab === 'write' }"
                @click.prevent="activeTab = 'write'"
                >Write</a
              >
            </li>
            <li>
              <a
                href="#"
                :class="{ active: activeTab === 'preview' }"
                @click.prevent="activeTab = 'preview'"
                >Preview</a
              >
            </li>
          </ul>
          <ul>
            <li>
              <button
                v-if="currentNote"
                @click="saveNote"
                :disabled="!hasUnsavedChanges"
                :class="{ 'secondary outline': !hasUnsavedChanges }"
              >
                {{ hasUnsavedChanges ? "Save" : "Saved" }}
              </button>
            </li>
            <li>
              <button
                v-if="currentNote"
                @click="deleteNote"
                class="secondary outline"
              >
                Delete
              </button>
            </li>
          </ul>
        </nav>

        <div v-if="activeTab === 'write'">
          <textarea
            v-if="currentNote"
            v-model="currentNote.content"
            @input="updateNote"
            placeholder="Start writing your markdown note here..."
          ></textarea>
          <p v-else>
            Select a note from the sidebar or create a new one to start writing.
          </p>
        </div>

        <div v-if="activeTab === 'preview'">
          <div v-if="currentNote" v-html="markdownPreview"></div>
          <p v-else>Select a note to see the preview.</p>
        </div>
      </article>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { marked } from "marked";

export default {
  name: "Notepad",
  data() {
    return {
      notes: [],
      currentNote: null,
      searchQuery: "",
      activeTab: "write",
      hasUnsavedChanges: false,
      loading: false,
      error: null,
    };
  },
  computed: {
    filteredNotes() {
      if (!this.searchQuery) return this.notes;

      const query = this.searchQuery.toLowerCase();
      return this.notes.filter(
        (note) =>
          note.title.toLowerCase().includes(query) ||
          note.content.toLowerCase().includes(query)
      );
    },
    markdownPreview() {
      if (!this.currentNote?.content) return "<p>No content to preview.</p>";
      return marked(this.currentNote.content);
    },
  },
  methods: {
    async loadNotes() {
      try {
        this.loading = true;
        const response = await axios.get("/api/notes");
        this.notes = response.data;

        // Select the first note if available
        if (this.notes.length > 0 && !this.currentNote) {
          this.selectNote(this.notes[0]);
        }
      } catch (error) {
        this.error = "Failed to load notes";
        console.error("Error loading notes:", error);
      } finally {
        this.loading = false;
      }
    },

    createNewNote() {
      const newNote = {
        title: "Untitled Note",
        content: "# Welcome to your new note!\n\nStart writing here..."
      };

      this.notes.unshift(newNote);
      this.selectNote(newNote);
      this.hasUnsavedChanges = true;
    },

    selectNote(note) {
      if (this.hasUnsavedChanges) {
        if (
          !confirm("You have unsaved changes. Do you want to discard them?")
        ) {
          return;
        }
      }

      this.currentNote = { ...note };
      this.hasUnsavedChanges = false;
      this.activeTab = "write";
    },

    updateNote() {
      if (this.currentNote) {
        this.hasUnsavedChanges = true;
        this.currentNote.last_modified = new Date().toISOString();

        // Update preview text
        this.currentNote.preview =
          this.currentNote.content
            .replace(/[#*`\[\]()]/g, "")
            .substring(0, 100) + "...";

        // Find the note in the notes array and update its properties
        const index = this.notes.findIndex(
          (n) => n._id === this.currentNote._id
        );
        if (index !== -1) {
          // Create a new object to ensure reactivity
          this.notes[index] = {
            ...this.notes[index],
            title: this.currentNote.title,
            content: this.currentNote.content,
            last_modified: this.currentNote.last_modified,
            preview: this.currentNote.preview,
          };
        }
      }
    },

    async saveNote() {
      if (!this.currentNote) return;

      try {
        this.currentNote.last_modified = new Date().toISOString(); // Use last_modified to match backend

        let response;
        if (this.currentNote._id) {
          // Existing note, use PUT
          response = await axios.put(`/api/notes/${this.currentNote._id}`, this.currentNote);
        } else {
          // New note, use POST
          response = await axios.post("/api/notes", this.currentNote);
          this.currentNote._id = response.data._id; // Get the new ID from the backend
        }

        // Update the note in the list
        const index = this.notes.findIndex(
          (n) => n._id === this.currentNote._id
        );
        if (index !== -1) {
          this.notes[index] = { ...this.currentNote };
        } else {
          // If it's a new note, add it to the beginning of the list
          this.notes.unshift({ ...this.currentNote });
        }

        this.hasUnsavedChanges = false;
      } catch (error) {
        this.error = "Failed to save note";
        console.error("Error saving note:", error);
      }
    },

    async deleteNote() {
      if (!this.currentNote) return;

      if (
        !confirm(`Are you sure you want to delete "${this.currentNote.title}"?`)
      ) {
        return;
      }

      try {
        await axios.delete(`/api/notes/${this.currentNote._id}`);

        this.notes = this.notes.filter(
          (n) => n._id !== this.currentNote._id
        );
        this.currentNote = null;
        this.hasUnsavedChanges = false;

        // Select the first note if available
        if (this.notes.length > 0) {
          this.selectNote(this.notes[0]);
        }
      } catch (error) {
        this.error = "Failed to delete note";
        console.error("Error deleting note:", error);
      }
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString();
    },
  },

  mounted() {
    this.loadNotes();
  },

  beforeUnmount() {
    if (this.hasUnsavedChanges) {
      if (confirm("You have unsaved changes. Do you want to save them?")) {
        this.saveNote();
      }
    }
  },
};
</script>

<style scoped>
.notes-items li.active {
  border-left: 4px solid;
  border-color: var(--pico-primary-background);
}

.notes-items li a {
  display: block;
  padding: var(--pico-spacing);
  text-decoration: none;
  color: inherit;
}

.notes-items li a:hover {
  opacity: 0.7;
}

textarea {
  min-height: 300px;
}

aside,
article {
  padding: var(--pico-spacing);
}
.notes-grid {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.notes-list-item {
  display: flex !important;
  flex-direction: column;
}
.sidebar {
  width: 20vw;
  padding-left: 0px !important;
}
.content {
  width: 100%;
}
</style>
