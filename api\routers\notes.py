"""
Notes Router

Handles note management CRUD operations.
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException
from database import (
    add_note, get_notes, get_note, update_note, 
    delete_note, get_all_tags
)
from api.models import Note, NoteCreate, NoteUpdate, SuccessResponse
from api.utils import handle_database_error

router = APIRouter(prefix="/api/notes", tags=["notes"])


@router.get("", response_model=List[Note])
@router.get("/", response_model=List[Note])
async def list_notes(
    search_query: Optional[str] = None, 
    tags: Optional[str] = None
) -> List[Note]:
    """
    Get all notes with optional search and tag filters.
    
    Args:
        search_query: Optional search term to filter notes
        tags: Optional comma-separated tags to filter by
        
    Returns:
        List of notes matching the filters
        
    Raises:
        HTTPException: If retrieval fails
    """
    try:
        notes_data = get_notes(search_query=search_query, tags=tags)
        return [Note(**note) for note in notes_data]
    except Exception as e:
        raise handle_database_error("retrieving notes", e)


@router.post("", response_model=Note)
@router.post("/", response_model=Note)
async def create_note(note_in: NoteCreate) -> Note:
    """
    Create a new note.
    
    Args:
        note_in: Note creation data
        
    Returns:
        Created note with database ID
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        note_id = add_note(
            title=note_in.title, 
            content=note_in.content, 
            tags=note_in.tags
        )
        created_note = get_note(note_id)
        
        if not created_note:
            raise HTTPException(status_code=500, detail="Note creation failed")
            
        return Note(**created_note)
        
    except Exception as e:
        raise handle_database_error("creating note", e)


@router.get("/{note_id}", response_model=Note)
async def get_note_by_id(note_id: str) -> Note:
    """
    Get a single note by ID.
    
    Args:
        note_id: ID of the note to retrieve
        
    Returns:
        Note with the specified ID
        
    Raises:
        HTTPException: If note not found or retrieval fails
    """
    try:
        note = get_note(note_id)
        if not note:
            raise HTTPException(status_code=404, detail="Note not found")
            
        return Note(**note)
        
    except Exception as e:
        raise handle_database_error("retrieving note", e)


@router.put("/{note_id}", response_model=Note)
async def update_note_by_id(note_id: str, note_in: NoteUpdate) -> Note:
    """
    Update an existing note.
    
    Args:
        note_id: ID of the note to update
        note_in: Note update data
        
    Returns:
        Updated note
        
    Raises:
        HTTPException: If update fails or note not found
    """
    try:
        update_note(
            note_id=note_id,
            title=note_in.title,
            content=note_in.content,
            tags=note_in.tags
        )
        
        updated_note = get_note(note_id)
        if not updated_note:
            raise HTTPException(status_code=404, detail="Note not found after update")
            
        return Note(**updated_note)
        
    except Exception as e:
        raise handle_database_error("updating note", e)


@router.delete("/{note_id}", response_model=SuccessResponse)
async def delete_note_by_id(note_id: str) -> SuccessResponse:
    """
    Delete a note by ID.
    
    Args:
        note_id: ID of the note to delete
        
    Returns:
        Success confirmation message
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        delete_note(note_id)
        return SuccessResponse(message="Note deleted successfully")
        
    except Exception as e:
        raise handle_database_error("deleting note", e)


@router.get("/tags/", response_model=List[str])
async def get_tags() -> List[str]:
    """
    Get all unique tags from notes.
    
    Returns:
        List of unique tags
        
    Raises:
        HTTPException: If retrieval fails
    """
    try:
        return get_all_tags()
    except Exception as e:
        raise handle_database_error("retrieving tags", e)