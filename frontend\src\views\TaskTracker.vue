<template>
  <div class="container">
    <hgroup>
      <h1>Task Tracker</h1>
      <p>Organize and track your tasks efficiently</p>
    </hgroup>
    <div class="grid">
      <article class="col">
        <h3>Total Tasks</h3>
        <p>{{ tasks.length }}</p>
      </article>
      <article class="col">
        <h3>Pending</h3>
        <p>{{ pendingTasks.length }}</p>
      </article>
      <article class="col">
        <h3>Completed</h3>
        <p>{{ completedTasks.length }}</p>
      </article>
      <article class="col">
        <h3>Completion Rate</h3>
        <p>{{ completionRate }}%</p>
      </article>
    </div>
    <nav>
      <ul>
        <li>
          <input
            v-model="searchQuery"
            type="search"
            placeholder="Search tasks..."
          />
        </li>
      </ul>
      <ul>
        <li>
          <button @click="showAddTaskModal = true" class="contrast">
            Add New Task
          </button>
        </li>
      </ul>
    </nav>
    <dialog :open="showAddTaskModal">
      <article>
        <label for="task-description">Task Description</label>
        <input
          v-model="newTask.text"
          type="text"
          id="task-description"
          placeholder="Enter task description..."
          required
        />
        <label for="priority">Priority</label>
        <select v-model="newTask.priority" id="priority">
          <option value="Low">Low</option>
          <option value="Medium">Medium</option>
          <option value="High">High</option>
        </select>

        <label for="notes">Notes (Optional)</label>
        <textarea
          v-model="newTask.notes"
          id="notes"
          placeholder="Additional notes..."
          rows="3"
        ></textarea>
        <footer>
          <button type="button" @click="addTaskAndCloseModal">Add Task</button>
          <button
            type="button"
            class="secondary"
            @click="showAddTaskModal = false"
          >
            Cancel
          </button>
        </footer>
      </article>
    </dialog>
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th></th>
            <th>Task</th>
            <th>Priority</th>
            <th>Added</th>
            <th>Completed</th>
            <th>Notes</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="task in filteredTasks"
            :key="task._id"
            :class="{ completed: task.status === 'completed' }"
          >
            <td>
              <button
                @click="toggleTask(task)"
                class="task-checkbox"
                :class="{ 'secondary outline': task.status === 'pending' }"
              >
                <span class="material-icons">{{
                  task.status === "completed" ? "&#10003;" : ""
                }}</span>
              </button>
            </td>
            <td>
              <span
                :class="{
                  'text-decoration-line-through': task.status === 'completed',
                }"
                >{{ task.text }}</span
              >
            </td>
            <td>
              <mark :class="task.priority.toLowerCase()">{{
                task.priority
              }}</mark>
            </td>
            <td>{{ formatDate(task.timestamp) }}</td>
            <td>
              <span v-if="task.completion_timestamp">{{
                formatDate(task.completion_timestamp)
              }}</span>
              <span v-else>-</span>
            </td>
            <td>
              <span v-if="task.notes">{{ task.notes }}</span>
              <span v-else>-</span>
            </td>
            <td>
              <button @click="deleteTask(task)" class="secondary outline cross">
                ⨯
              </button>
            </td>
          </tr>
          <tr v-if="filteredTasks.length === 0">
            <td colspan="7">{{ getNoTasksMessage() }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "TaskTracker",
  data() {
    return {
      tasks: [],
      newTask: {
        text: "",
        notes: "",
        priority: "Medium",
      },
      filterStatus: "all",
      searchQuery: "",
      loading: false,
      error: null,
      showAddTaskModal: false,
    };
  },
  computed: {
    pendingTasks() {
      return this.tasks.filter((task) => task.status === "pending");
    },
    completedTasks() {
      return this.tasks.filter((task) => task.status === "completed");
    },
    filteredTasks() {
      let filtered = this.tasks;

      // Filter by status
      if (this.filterStatus !== "all") {
        filtered = filtered.filter((task) => task.status === this.filterStatus);
      }

      // Filter by search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(
          (task) =>
            task.text.toLowerCase().includes(query) ||
            task.notes.toLowerCase().includes(query)
        );
      }

      return filtered.sort((a, b) => {
        // First sort by status: pending tasks first, completed tasks last
        if (a.status !== b.status) {
          if (a.status === 'pending' && b.status === 'completed') return -1;
          if (a.status === 'completed' && b.status === 'pending') return 1;
        }
        // Within same status, sort by newest timestamp first
        return new Date(b.timestamp) - new Date(a.timestamp);
      });
    },
    completionRate() {
      if (this.tasks.length === 0) return 0;
      return Math.round((this.completedTasks.length / this.tasks.length) * 100);
    },
  },
  methods: {
    async loadTasks() {
      try {
        this.loading = true;
        const response = await axios.get("/api/tasks/");
        this.tasks = response.data;
      } catch (error) {
        this.error = "Failed to load tasks";
        console.error("Error loading tasks:", error);
      } finally {
        this.loading = false;
      }
    },

    async addTask() {
      if (!this.newTask.text.trim()) return;

      try {
        const task = {
          text: this.newTask.text,
          notes: this.newTask.notes,
          priority: this.newTask.priority,
          status: "pending",
          completion_timestamp: null,
        };

        const response = await axios.post("/api/tasks", task);
        this.tasks.push(response.data);

        // Reset form
        this.newTask = {
          text: "",
          notes: "",
          priority: "Medium",
        };
        this.showAddTaskModal = false; // Close modal after adding task
      } catch (error) {
        this.error = "Failed to add task";
        console.error("Error adding task:", error);
      }
    },

    addTaskAndCloseModal() {
      this.addTask();
    },

    async toggleTask(task) {
      try {
        const newStatus = task.status === "pending" ? "completed" : "pending";
        const updatedTask = {
          ...task,
          status: newStatus,
          completion_timestamp:
            newStatus === "completed"
              ? new Date().toISOString().replace("T", " ").slice(0, 19)
              : null,
        };

        await axios.put(`/api/tasks/${task._id}`, updatedTask);

        const index = this.tasks.findIndex(
          (t) => t._id === task._id
        );
        if (index !== -1) {
          this.tasks[index] = updatedTask;
        }
      } catch (error) {
        this.error = "Failed to update task";
        console.error("Error updating task:", error);
      }
    },

    async deleteTask(task) {
      if (!confirm("Are you sure you want to delete this task?")) return;

      try {
        await axios.delete(`/api/tasks/${task._id}`);
        this.tasks = this.tasks.filter((t) => t._id !== task._id);
      } catch (error) {
        this.error = "Failed to delete task";
        console.error("Error deleting task:", error);
      }
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffMins < 1) return 'just now';
      if (diffMins < 60) return `${diffMins}m ago`;
      if (diffHours < 24) return `${diffHours}h ago`;
      if (diffDays < 7) return `${diffDays}d ago`;
      
      // Fallback to readable date for older items
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    },
    getNoTasksMessage() {
      if (this.searchQuery) {
        return "No tasks found matching your search criteria.";
      } else {
        return "No tasks yet. Add a new task to get started!";
      }
    },
  },

  mounted() {
    this.loadTasks();
  },
};
</script>

<style scoped>
.text-decoration-line-through {
  text-decoration: line-through;
}
.task-checkbox {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
}
.cross{
  border: none;
  font-size: 2rem;
  color: gray;
}
</style>
