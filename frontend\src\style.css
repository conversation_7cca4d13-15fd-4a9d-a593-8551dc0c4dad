@import url("https://cdn.jsdelivr.net/npm/@picocss/pico@2.0.6/css/pico.min.css");
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

* {
  font-family: "Nunito", sans-serif !important;
}

#app {
  min-height: 100vh;
}
header {
  padding: 0.5rem 0;
}
article header {
  min-height: 20vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
}

/* Basic Switch Styles (can be customized further) */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Row styles: uses flexbox to arrange columns */
.row {
  display: flex;
  flex-wrap: wrap; /* Allows columns to wrap to the next line */
  margin-right: -15px; /* Negative margin to offset column padding */
  margin-left: -15px; /* Negative margin to offset column padding */
}

/* Column base styles: apply padding and default to full width on small screens */
.col {
  padding-right: 15px;
  padding-left: 15px;
  box-sizing: border-box;
  flex-basis: 100%; /* Default to full width for mobile-first approach */
  max-width: 100%;
}

/* Generate column classes for different widths (1 to 12) */
/* This loop generates classes like .col-1, .col-2, ..., .col-12 */
/* For a 12-column grid, each column takes (width / 12 * 100)% */
/* We'll use CSS variables or a preprocessor for a real loop, but for minimal, we'll list them */

.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}
.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}
.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}
.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}
.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}
.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}
.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}
.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}
.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}
.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}
.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
mark {
  border-radius: 5px;
  font-size: 14px;
}