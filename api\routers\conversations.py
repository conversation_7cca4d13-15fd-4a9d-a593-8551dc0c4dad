"""
Conversations Router

Handles LLM conversation simulation endpoints with incremental chat-like responses.
"""

from fastapi import APIRouter, HTTPException
from agents.conversation_session import (
    create_conversation_session, get_conversation_session, cleanup_old_sessions
)
from agents.llm_conversation_agent import start_conversation  # For legacy support
from database import save_conversation, get_conversations
from api.models import (
    ConversationStartRequest, ConversationStartResponse,
    ConversationContinueRequest, ConversationContinueResponse,
    ConversationSummaryResponse, ConversationMessage,
    # Legacy models
    ConversationRequest, ConversationResponse
)

router = APIRouter(prefix="/api/conversations", tags=["conversations"])


@router.post("/start-chat", response_model=ConversationStartResponse)
async def start_chat_conversation(request: ConversationStartRequest) -> ConversationStartResponse:
    """
    Start a new chat-like conversation that returns immediately with the first message.
    
    Args:
        request: Conversation start parameters
        
    Returns:
        ConversationStartResponse with first message and session info
    """
    try:
        # Clean up old sessions periodically
        cleanup_old_sessions()
        
        # Create new session
        session_id = create_conversation_session(use_gemini=request.use_gemini)
        session = get_conversation_session(session_id)
        
        if not session:
            return ConversationStartResponse(
                success=False,
                error="Failed to create conversation session"
            )
        
        # Generate first message
        result = session.generate_next_message(request.seed_question)
        
        if result['success']:
            return ConversationStartResponse(
                success=True,
                session_id=session_id,
                message=ConversationMessage(**result['message']),
                next_agent=result['next_agent']
            )
        else:
            return ConversationStartResponse(
                success=False,
                session_id=session_id,
                error="Failed to generate first message"
            )
            
    except Exception as e:
        print(f"Chat conversation start error: {str(e)}")
        return ConversationStartResponse(
            success=False,
            error=f"Failed to start conversation: {str(e)}"
        )


@router.post("/continue", response_model=ConversationContinueResponse)
async def continue_conversation(request: ConversationContinueRequest) -> ConversationContinueResponse:
    """
    Continue an existing conversation with the next message.
    
    Args:
        request: Conversation continue parameters
        
    Returns:
        ConversationContinueResponse with next message
    """
    try:
        session = get_conversation_session(request.session_id)
        
        if not session:
            return ConversationContinueResponse(
                success=False,
                error="Conversation session not found"
            )
        
        # Generate next message
        result = session.generate_next_message()
        
        if result['success']:
            return ConversationContinueResponse(
                success=True,
                session_id=request.session_id,
                message=ConversationMessage(**result['message']),
                next_agent=result['next_agent'],
                conversation_length=result['conversation_length'],
                has_error=result.get('has_error', False)
            )
        else:
            return ConversationContinueResponse(
                success=False,
                session_id=request.session_id,
                error="Failed to generate next message"
            )
            
    except Exception as e:
        print(f"Continue conversation error: {str(e)}")
        return ConversationContinueResponse(
            success=False,
            session_id=request.session_id,
            error=f"Failed to continue conversation: {str(e)}"
        )


@router.get("/summary/{session_id}", response_model=ConversationSummaryResponse)
async def get_conversation_summary(session_id: str) -> ConversationSummaryResponse:
    """
    Get the full conversation history for a session.
    
    Args:
        session_id: ID of the conversation session
        
    Returns:
        ConversationSummaryResponse with full conversation history
    """
    try:
        session = get_conversation_session(session_id)
        
        if not session:
            return ConversationSummaryResponse(
                success=False,
                error="Conversation session not found"
            )
        
        summary = session.get_conversation_summary()
        conversation_messages = [
            ConversationMessage(**msg) for msg in summary['conversation']
        ]
        
        return ConversationSummaryResponse(
            success=True,
            session_id=session_id,
            seed_question=summary['seed_question'],
            conversation=conversation_messages,
            created_at=summary['created_at'],
            message_count=summary['message_count'],
            next_agent=summary['next_agent']
        )
        
    except Exception as e:
        print(f"Get conversation summary error: {str(e)}")
        return ConversationSummaryResponse(
            success=False,
            session_id=session_id,
            error=f"Failed to get conversation summary: {str(e)}"
        )


# Legacy endpoint for backward compatibility
@router.post("/start", response_model=ConversationResponse)
async def start_conversation_endpoint(request: ConversationRequest) -> ConversationResponse:
    """
    Legacy endpoint: Start a conversation and return all messages at once.
    
    DEPRECATED: Use /start-chat and /continue for better user experience.
    """
    try:
        result = start_conversation(
            seed_question=request.seed_question,
            num_exchanges=request.num_exchanges,
            use_gemini=request.use_gemini
        )
        
        if result['success']:
            conversation_messages = [
                ConversationMessage(**msg) for msg in result['conversation']
            ]
            return ConversationResponse(
                success=True,
                conversation_id=result['conversation_id'],
                seed_question=result['seed_question'],
                num_exchanges=result['num_exchanges'],
                conversation=conversation_messages,
                created_at=result['created_at']
            )
        else:
            return ConversationResponse(
                success=False,
                error=result['error']
            )
            
    except Exception as e:
        print(f"Legacy conversation error: {str(e)}")
        return ConversationResponse(
            success=False,
            error=f"Failed to start conversation: {str(e)}"
        )


@router.post("/save")
async def save_conversation_endpoint(request: dict) -> dict:
    """
    Save a conversation to MongoDB.
    
    Args:
        request: Dict containing session_id, seed_question, conversation, and created_at
        
    Returns:
        Dict with success status and conversation_id if successful
    """
    try:
        conversation_id = save_conversation(
            session_id=request['session_id'],
            seed_question=request['seed_question'],
            conversation_messages=request['conversation'],
            created_at=request['created_at']
        )
        
        return {
            "success": True,
            "conversation_id": conversation_id,
            "message": "Conversation saved successfully"
        }
        
    except Exception as e:
        print(f"Save conversation error: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to save conversation: {str(e)}"
        }


@router.get("/history")
async def get_conversation_history() -> dict:
    """
    Get all saved conversations from MongoDB.
    
    Returns:
        Dict with success status and conversations list
    """
    try:
        conversations = get_conversations()
        return {
            "success": True,
            "conversations": conversations
        }
        
    except Exception as e:
        print(f"Get conversation history error: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to get conversation history: {str(e)}",
            "conversations": []
        }