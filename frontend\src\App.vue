<template>
  <div id="app">
    <header>
      <nav class="container">
        <ul>
          <li>
            <router-link to="/"><strong>Productivity Tools ✨</strong></router-link>
          </li>
        </ul>
        <ul>
          <li>
            <ThemeToggle />
          </li>
        </ul>
      </nav>
    </header>
    <main class="container">
      <router-view />
    </main>
  </div>
</template>

<script>
import ThemeToggle from "./components/ThemeToggle.vue";

export default {
  name: "App",
  components: {
    ThemeToggle,
  },
};
</script>

<style scoped>
/* No scoped styles needed for App.vue as Pico.css handles most of it */
</style>
