<template>
  <div class="container">
    <hgroup>
      <h1>Productivity Tools Dashboard</h1>
      <p>Choose a tool to get started with your productivity workflow</p>
    </hgroup>
    
    <div class="grid">
      <ToolCard
        v-for="tool in tools"
        :key="tool.name"
        :title="tool.title"
        :description="tool.description"
        :icon="tool.icon"
        :route="tool.route"
        @click="$router.push(tool.route)"
      />
    </div>
  </div>
</template>

<script>
import ToolCard from '../components/ToolCard.vue'

export default {
  name: 'Dashboard',
  components: {
    ToolCard
  },
  data() {
    return {
      tools: [
        {
          name: 'text-readability',
          title: 'Text Readability Editor',
          description: 'Improve text readability using AI-powered analysis and suggestions',
          icon: '📝',
          route: '/text-readability-editor'
        },
        {
          name: 'task-tracker',
          title: 'Task Tracker',
          description: 'Organize and track your tasks with this efficient task management tool',
          icon: '✅',
          route: '/task-tracker'
        },
        {
          name: 'password-generator',
          title: 'Secure Password Generator',
          description: 'Generate strong, secure passwords with customizable options',
          icon: '🔐',
          route: '/password-generator'
        },
        {
          name: 'notepad',
          title: 'Markdown Notepad',
          description: 'Create and manage your notes with markdown support',
          icon: '📄',
          route: '/notepad'
        },
        {
          name: 'audio-recorder',
          title: 'Audio Recorder',
          description: 'Record and manage audio files directly in your browser',
          icon: '🎤',
          route: '/audio-recorder'
        },
        {
          name: 'deep-conversations',
          title: 'Deep Conversations',
          description: 'Watch AI agents engage in thoughtful dialogue on meaningful topics',
          icon: '🤖',
          route: '/deep-conversations'
        }
      ]
    }
  }
}
</script>

<style scoped>
/* No scoped styles needed as Pico.css handles most of it */
</style>