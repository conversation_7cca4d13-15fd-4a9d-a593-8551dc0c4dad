# API Architecture Documentation

## Overview

The backend has been refactored using clean code principles with modular FastAPI routers for better maintainability and separation of concerns.

## Project Structure

```
api/
├── __init__.py
├── models.py          # Shared Pydantic models
├── utils.py           # Utility functions and error handlers
├── routers/
│   ├── __init__.py
│   ├── text_readability.py    # Text processing endpoints
│   ├── tasks.py               # Task management CRUD
│   ├── notes.py               # Note management CRUD  
│   └── conversations.py       # LLM conversation endpoints
└── README.md
```

## Key Improvements

### 1. **Modular Architecture**
- Separated concerns into logical routers
- Each router handles a specific domain (tasks, notes, conversations, etc.)
- Shared models and utilities for consistency

### 2. **Clean Code Principles**
- Single Responsibility Principle: Each module has one clear purpose
- DRY (Don't Repeat Yourself): Shared models and error handlers
- Consistent error handling across all endpoints
- Proper type hints and documentation

### 3. **Modern FastAPI Features**
- Used modern `lifespan` context manager instead of deprecated `@app.on_event`
- Proper dependency injection patterns
- Consistent response models

### 4. **Better Error Handling**
- Centralized error handling in `utils.py`
- Consistent HTTP status codes
- Proper error messages and details

## Router Details

### Text Readability Router (`/api/text-readability/`)
- `POST /` - Process text for readability improvement

### Tasks Router (`/api/tasks/`)
- `GET /` - List all tasks
- `POST /` - Create new task
- `PUT /{task_id}` - Update existing task
- `DELETE /{task_id}` - Delete task

### Notes Router (`/api/notes/`)
- `GET /` - List notes (with optional search/tag filters)
- `POST /` - Create new note
- `GET /{note_id}` - Get specific note
- `PUT /{note_id}` - Update existing note
- `DELETE /{note_id}` - Delete note
- `GET /tags/` - Get all unique tags

### Conversations Router (`/api/conversations/`)
- `POST /start` - Start new LLM conversation

## Migration Notes

- Original `backend.py` backed up as `backend_original.py`
- New `main.py` contains the refactored application
- `backend.py` is now a symlink to `main.py` for backward compatibility
- All existing functionality preserved
- Database migration logic moved to utilities

## Usage

The refactored backend maintains 100% API compatibility with the previous version while providing better code organization and maintainability.

```python
# Start the server
python main.py

# Or use the symlinked backend.py
python backend.py
```